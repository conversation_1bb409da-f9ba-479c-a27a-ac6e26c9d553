#!/usr/bin/env python3
"""
创建测试用的Excel文件，用于验证.xls和.xlsx格式的差异
"""

import os
import sys

try:
    import openpyxl
    from openpyxl import Workbook
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import xlwt
    XLWT_AVAILABLE = True
except ImportError:
    XLWT_AVAILABLE = False


def create_test_xlsx(file_path: str):
    """创建测试用的.xlsx文件"""
    if not OPENPYXL_AVAILABLE:
        print("❌ openpyxl不可用，无法创建.xlsx文件")
        return False
    
    wb = Workbook()
    ws = wb.active
    
    # 表头信息 (第1-4行)
    ws['A1'] = 'Project Name'
    ws['B1'] = 'TestProject'
    ws['A2'] = 'Sub System'
    ws['B2'] = 'TestSubSystem'
    ws['A3'] = 'Module Name'
    ws['B3'] = 'TestModule'
    ws['A4'] = 'BASE ADDR'
    ws['B4'] = '0x1000'
    
    # 第5-9行留空或填充其他信息
    
    # 第10行：寄存器表头
    headers = ['Offset', 'RegName', '', '', 'Width', '', '', 'Bit', 'FieldName', 'RW', 'ResetValue', 'Set/Clear']
    for col, header in enumerate(headers, 1):
        ws.cell(row=10, column=col, value=header)
    
    # 第11行：Register group（跳过）
    ws['A11'] = 'Register group'
    
    # 第12行开始：寄存器数据
    test_data = [
        # 第一个寄存器
        ['0x0000', 'TEST_REG1', '', '', '32', '', '', '[31:24]', 'FIELD_A', 'RW', '0x00', ''],
        ['', '', '', '', '', '', '', '[23:16]', 'FIELD_B', 'RO', '0xFF', ''],
        ['', '', '', '', '', '', '', '[15:8]', 'FIELD_C', 'RW', '0x55', 'Yes'],
        ['', '', '', '', '', '', '', '[7:0]', 'Reserved', 'RO', '0x00', ''],
        
        # 第二个寄存器
        ['0x0004', 'TEST_REG2', '', '', '32', '', '', '[31:0]', 'FULL_FIELD', 'RW', '0x12345678', ''],
        
        # 第三个寄存器
        ['0x0008', 'TEST_REG3', '', '', '16', '', '', '[15]', 'ENABLE', 'RW', '0x1', ''],
        ['', '', '', '', '', '', '', '[14:8]', 'CONFIG', 'RW', '0x7F', ''],
        ['', '', '', '', '', '', '', '[7:0]', 'STATUS', 'RO', '0x00', ''],
    ]
    
    for row_idx, row_data in enumerate(test_data, 12):
        for col_idx, value in enumerate(row_data, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    wb.save(file_path)
    print(f"✅ 创建.xlsx文件: {file_path}")
    return True


def create_test_xls(file_path: str):
    """创建测试用的.xls文件"""
    if not XLWT_AVAILABLE:
        print("❌ xlwt不可用，无法创建.xls文件")
        return False
    
    wb = xlwt.Workbook()
    ws = wb.add_sheet('Sheet1')
    
    # 表头信息 (第1-4行)
    ws.write(0, 0, 'Project Name')
    ws.write(0, 1, 'TestProject')
    ws.write(1, 0, 'Sub System')
    ws.write(1, 1, 'TestSubSystem')
    ws.write(2, 0, 'Module Name')
    ws.write(2, 1, 'TestModule')
    ws.write(3, 0, 'BASE ADDR')
    ws.write(3, 1, '0x1000')
    
    # 第10行：寄存器表头
    headers = ['Offset', 'RegName', '', '', 'Width', '', '', 'Bit', 'FieldName', 'RW', 'ResetValue', 'Set/Clear']
    for col, header in enumerate(headers):
        ws.write(9, col, header)  # 第10行，索引为9
    
    # 第11行：Register group（跳过）
    ws.write(10, 0, 'Register group')  # 第11行，索引为10
    
    # 第12行开始：寄存器数据
    test_data = [
        # 第一个寄存器
        ['0x0000', 'TEST_REG1', '', '', '32', '', '', '[31:24]', 'FIELD_A', 'RW', '0x00', ''],
        ['', '', '', '', '', '', '', '[23:16]', 'FIELD_B', 'RO', '0xFF', ''],
        ['', '', '', '', '', '', '', '[15:8]', 'FIELD_C', 'RW', '0x55', 'Yes'],
        ['', '', '', '', '', '', '', '[7:0]', 'Reserved', 'RO', '0x00', ''],
        
        # 第二个寄存器
        ['0x0004', 'TEST_REG2', '', '', '32', '', '', '[31:0]', 'FULL_FIELD', 'RW', '0x12345678', ''],
        
        # 第三个寄存器
        ['0x0008', 'TEST_REG3', '', '', '16', '', '', '[15]', 'ENABLE', 'RW', '0x1', ''],
        ['', '', '', '', '', '', '', '[14:8]', 'CONFIG', 'RW', '0x7F', ''],
        ['', '', '', '', '', '', '', '[7:0]', 'STATUS', 'RO', '0x00', ''],
    ]
    
    for row_idx, row_data in enumerate(test_data):
        for col_idx, value in enumerate(row_data):
            ws.write(11 + row_idx, col_idx, value)  # 第12行开始，索引为11
    
    wb.save(file_path)
    print(f"✅ 创建.xls文件: {file_path}")
    return True


def main():
    """主函数"""
    print("🔧 创建测试用Excel文件")
    print("=" * 40)
    
    # 检查依赖库
    print("📦 依赖库检查:")
    print(f"  openpyxl: {'✅ 可用' if OPENPYXL_AVAILABLE else '❌ 不可用'}")
    print(f"  xlwt: {'✅ 可用' if XLWT_AVAILABLE else '❌ 不可用'}")
    
    if not OPENPYXL_AVAILABLE:
        print("  安装命令: pip install openpyxl")
    if not XLWT_AVAILABLE:
        print("  安装命令: pip install xlwt")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 创建测试文件
    xlsx_file = os.path.join(current_dir, 'test_register_table.xlsx')
    xls_file = os.path.join(current_dir, 'test_register_table.xls')
    
    success_count = 0
    
    if create_test_xlsx(xlsx_file):
        success_count += 1
    
    if create_test_xls(xls_file):
        success_count += 1
    
    print(f"\n📊 创建结果: {success_count}/2 个文件创建成功")
    
    if success_count > 0:
        print("\n🔍 现在可以运行调试脚本:")
        print("  python debug_xls_xlsx.py")


if __name__ == "__main__":
    main()
