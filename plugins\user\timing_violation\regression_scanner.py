"""
回归目录扫描器

提供回归目录的递归扫描功能，支持vio_summary.log文件发现和目录结构解析。
支持两种扫描模式：
1. 标准模式：./regression/<subsys>/.../<case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log
2. 通用模式：./regression/任意层级目录/<case_name>_<seed_number>/log/vio_summary.log
"""

import os
import re
import time
from typing import List, Dict, Optional, Tuple, Callable
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal, QThread


@dataclass
class RegressionFileInfo:
    """回归文件信息"""
    file_path: str              # 完整文件路径
    subsys: str                 # 子系统名称
    corner_name: str            # 工艺角名称
    case_name: str              # 用例名称
    seed: str                   # 种子号
    relative_path: str          # 相对于回归根目录的路径
    file_size: int              # 文件大小（字节）
    modified_time: float        # 文件修改时间


@dataclass
class RegressionScanResult:
    """回归扫描结果"""
    total_files: int                        # 总文件数
    valid_files: List[RegressionFileInfo]   # 有效的vio_summary.log文件
    invalid_paths: List[str]                # 无效路径
    scan_time: float                        # 扫描耗时
    subsys_groups: Dict[str, List[RegressionFileInfo]]  # 按子系统分组
    corner_groups: Dict[str, List[RegressionFileInfo]]  # 按工艺角分组
    case_groups: Dict[str, List[RegressionFileInfo]]    # 按用例分组


class RegressionDirectoryScanner(QObject):
    """回归目录扫描器"""
    
    # 信号定义
    scan_progress = pyqtSignal(int, str)  # 进度百分比, 状态信息
    scan_completed = pyqtSignal(object)   # 扫描完成，返回RegressionScanResult
    scan_failed = pyqtSignal(str)         # 扫描失败，返回错误信息
    file_found = pyqtSignal(object)       # 发现文件，返回RegressionFileInfo
    
    def __init__(self, use_standard_structure: bool = True):
        super().__init__()
        
        # 扫描模式
        self.use_standard_structure = use_standard_structure
        
        # 目录结构正则表达式
        # 标准模式: <case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log
        self.case_corner_pattern = re.compile(r'^(.+)_(\w+_\w+_\w+)$')
        self.case_seed_pattern = re.compile(r'(.+)_(\d+)$')
        
        # 支持的corner列表
        self.valid_corners = [
            'npg_f1_ssg', 'npg_f2_ssg', 'npg_f3_ssg', 'npg_f4_ssg', 'npg_f5_ssg', 'npg_f6_ssg', 'npg_f7_ssg',
            'npg_f1_ffg', 'npg_f2_ffg', 'npg_f3_ffg', 'npg_f4_ffg', 'npg_f5_ffg', 'npg_f6_ffg', 'npg_f7_ffg',
            'npg_f1_tt', 'npg_f2_tt', 'npg_f3_tt'
        ]
        
        # 子系统识别模式
        self.subsys_patterns = [
            re.compile(r'.*_sys$'),  # 以_sys结尾
            re.compile(r'^top$'),    # top目录
            re.compile(r'.*_subsys$'), # 以_subsys结尾
        ]
        
        # 获取有效的子系统列表
        self.valid_subsys = self._get_valid_subsys_list()
        
        # 扫描统计
        self.scan_stats = {
            'directories_scanned': 0,
            'files_found': 0,
            'valid_files': 0,
            'invalid_paths': 0
        }
    
    def _get_valid_subsys_list(self) -> List[str]:
        """获取有效的子系统列表
        
        从$PROJ_ENV环境变量指定的目录中扫描以_sys结尾或名为top的目录
        
        Returns:
            List[str]: 有效的子系统列表
        """
        valid_subsys = []
        
        try:
            proj_env = os.environ.get('PROJ_ENV')
            if proj_env and os.path.exists(proj_env):
                for item in os.listdir(proj_env):
                    item_path = os.path.join(proj_env, item)
                    if os.path.isdir(item_path):
                        if item.endswith('_sys') or item == 'top':
                            valid_subsys.append(item)
        except Exception as e:
            print(f"警告: 无法从PROJ_ENV获取子系统列表: {e}")
        
        # 如果没有找到任何子系统，添加一些默认值
        if not valid_subsys:
            valid_subsys = ['top', 'apcpu_sys', 'gpu_sys', 'ap_sys', 'dbg_sys', 'aon_sys', 'pcie_sys', 'ai_sys',
                'dpu_vsp_sys', 'vdec_sys', 'vpu_sys', 'pub_sys', 'mm_sys', 'camera_sys', 'lpach_sys', 'aud_sys', 
                'audio_sys', 'ch_sys', 'sp_sys', 'spch_sys', 'phy_cp_sys', 'ps_cp_sys', 'cp_uni_sys', 'cp_dl_sys']
        
        return sorted(valid_subsys)
    
    def scan_regression_directory(self, regression_root: str, 
                                progress_callback: Optional[Callable[[int, str], None]] = None) -> RegressionScanResult:
        """扫描回归目录
        
        Args:
            regression_root: 回归根目录路径
            progress_callback: 进度回调函数
            
        Returns:
            RegressionScanResult: 扫描结果
            
        Raises:
            FileNotFoundError: 目录不存在
            PermissionError: 权限不足
        """
        start_time = time.time()
        
        if not os.path.exists(regression_root):
            raise FileNotFoundError(f"回归目录不存在: {regression_root}")
        
        if not os.path.isdir(regression_root):
            raise ValueError(f"路径不是目录: {regression_root}")
        
        # 重置统计
        self.scan_stats = {
            'directories_scanned': 0,
            'files_found': 0,
            'valid_files': 0,
            'invalid_paths': 0
        }
        
        valid_files = []
        invalid_paths = []
        
        # 发送开始信号
        scan_mode = "标准模式" if self.use_standard_structure else "通用模式"
        if progress_callback:
            progress_callback(0, f"开始扫描回归目录 ({scan_mode}): {regression_root}")
        self.scan_progress.emit(0, f"开始扫描回归目录 ({scan_mode}): {regression_root}")
        
        try:
            # 递归扫描目录
            self._recursive_scan(regression_root, regression_root, valid_files, invalid_paths, progress_callback)
            
            # 计算扫描时间
            scan_time = time.time() - start_time
            
            # 创建分组
            subsys_groups = self._group_by_subsys(valid_files)
            corner_groups = self._group_by_corner(valid_files)
            case_groups = self._group_by_case(valid_files)
            
            # 创建扫描结果
            result = RegressionScanResult(
                total_files=len(valid_files) + len(invalid_paths),
                valid_files=valid_files,
                invalid_paths=invalid_paths,
                scan_time=scan_time,
                subsys_groups=subsys_groups,
                corner_groups=corner_groups,
                case_groups=case_groups
            )
            
            # 发送完成信号
            if progress_callback:
                progress_callback(100, f"扫描完成，发现 {len(valid_files)} 个有效文件")
            self.scan_completed.emit(result)
            
            return result
            
        except Exception as e:
            error_msg = f"扫描失败: {str(e)}"
            if progress_callback:
                progress_callback(-1, error_msg)
            self.scan_failed.emit(error_msg)
            raise
    
    def _recursive_scan(self, current_path: str, regression_root: str, 
                       valid_files: List[RegressionFileInfo], 
                       invalid_paths: List[str],
                       progress_callback: Optional[Callable[[int, str], None]] = None):
        """递归扫描目录"""
        try:
            self.scan_stats['directories_scanned'] += 1
            
            # 更新进度（每扫描10个目录更新一次）
            if self.scan_stats['directories_scanned'] % 10 == 0:
                status = f"已扫描 {self.scan_stats['directories_scanned']} 个目录，发现 {self.scan_stats['valid_files']} 个有效文件"
                if progress_callback:
                    progress_callback(-1, status)  # -1表示无法确定具体进度
                self.scan_progress.emit(-1, status)
            
            for item in os.listdir(current_path):
                item_path = os.path.join(current_path, item)
                
                if os.path.isdir(item_path):
                    # 递归扫描子目录
                    self._recursive_scan(item_path, regression_root, valid_files, invalid_paths, progress_callback)
                
                elif item == 'vio_summary.log':
                    # 发现vio_summary.log文件
                    self.scan_stats['files_found'] += 1
                    
                    # 解析文件路径
                    file_info = self._parse_file_path(item_path, regression_root)
                    
                    if file_info:
                        valid_files.append(file_info)
                        self.scan_stats['valid_files'] += 1
                        self.file_found.emit(file_info)
                    else:
                        invalid_paths.append(item_path)
                        self.scan_stats['invalid_paths'] += 1
                        
        except PermissionError:
            # 权限不足，跳过该目录
            invalid_paths.append(current_path)
            self.scan_stats['invalid_paths'] += 1
        except Exception as e:
            # 其他错误，记录但继续扫描
            invalid_paths.append(f"{current_path}: {str(e)}")
            self.scan_stats['invalid_paths'] += 1

    def _parse_file_path(self, file_path: str, regression_root: str) -> Optional[RegressionFileInfo]:
        """解析文件路径，提取目录结构信息

        Args:
            file_path: vio_summary.log文件的完整路径
            regression_root: 回归根目录

        Returns:
            RegressionFileInfo: 解析出的文件信息，如果解析失败返回None
        """
        if self.use_standard_structure:
            return self._parse_standard_structure(file_path, regression_root)
        else:
            return self._parse_flexible_structure(file_path, regression_root)
    
    def _parse_standard_structure(self, file_path: str, regression_root: str) -> Optional[RegressionFileInfo]:
        """解析标准目录结构
        
        格式: ./regression/<subsys>/.../<case_name>_<corner_name>/<case_name>_<seed_number>/log/vio_summary.log
        """
        try:
            # 获取相对路径
            relative_path = os.path.relpath(file_path, regression_root)

            # 分割路径组件
            path_parts = relative_path.split(os.sep)

            # 至少需要4层：subsys/.../case_corner/case_seed/log/vio_summary.log
            if len(path_parts) < 4:
                return None

            # 最后一个应该是vio_summary.log
            if path_parts[-1] != 'vio_summary.log':
                return None

            # 倒数第二个应该是log目录
            if path_parts[-2] != 'log':
                return None

            # 倒数第三个应该是case_seed格式
            case_seed_dir = path_parts[-3]
            case_seed_match = self.case_seed_pattern.match(case_seed_dir)
            if not case_seed_match:
                return None

            case_name = case_seed_match.group(1)
            seed = case_seed_match.group(2)

            # 倒数第四个应该是case_corner格式
            case_corner_dir = path_parts[-4]
            case_corner_match = self.case_corner_pattern.match(case_corner_dir)
            if not case_corner_match:
                return None

            case_name_from_corner = case_corner_match.group(1)
            corner_name = case_corner_match.group(2)

            # 验证case名称一致性
            if case_name != case_name_from_corner:
                return None

            # 查找子系统名称
            subsys = self._find_subsys_in_path(path_parts[:-4])
            if not subsys:
                # 如果没有找到明确的子系统，使用第一级目录作为子系统
                subsys = path_parts[0] if path_parts else "unknown"

            # 获取文件信息
            file_size = os.path.getsize(file_path)
            modified_time = os.path.getmtime(file_path)

            return RegressionFileInfo(
                file_path=file_path,
                subsys=subsys,
                corner_name=corner_name,
                case_name=case_name,
                seed=seed,
                relative_path=relative_path,
                file_size=file_size,
                modified_time=modified_time
            )

        except Exception as e:
            # 解析失败
            return None
    
    def _parse_flexible_structure(self, file_path: str, regression_root: str) -> Optional[RegressionFileInfo]:
        """解析通用目录结构
        
        格式: ./regression/任意层级目录/<case_name>_<seed_number>/log/vio_summary.log
        """
        try:
            # 获取相对路径
            relative_path = os.path.relpath(file_path, regression_root)

            # 分割路径组件
            path_parts = relative_path.split(os.sep)

            # 至少需要3层：.../case_seed/log/vio_summary.log
            if len(path_parts) < 3:
                return None

            # 最后一个应该是vio_summary.log
            if path_parts[-1] != 'vio_summary.log':
                return None

            # 倒数第二个应该是log目录
            if path_parts[-2] != 'log':
                return None

            # 倒数第三个应该是case_seed格式
            case_seed_dir = path_parts[-3]
            case_seed_match = self.case_seed_pattern.match(case_seed_dir)
            if not case_seed_match:
                return None

            case_name = case_seed_match.group(1)
            seed = case_seed_match.group(2)

            # 从路径中查找corner和subsys
            corner_name = self._find_corner_in_path(path_parts)
            subsys = self._find_subsys_in_path_flexible(path_parts)

            # 如果没有找到corner，设置为unknown
            if not corner_name:
                corner_name = "unknown"

            # 如果没有找到subsys，设置为unknown
            if not subsys:
                subsys = "unknown"

            # 获取文件信息
            file_size = os.path.getsize(file_path)
            modified_time = os.path.getmtime(file_path)

            return RegressionFileInfo(
                file_path=file_path,
                subsys=subsys,
                corner_name=corner_name,
                case_name=case_name,
                seed=seed,
                relative_path=relative_path,
                file_size=file_size,
                modified_time=modified_time
            )

        except Exception as e:
            # 解析失败
            return None

    def _find_corner_in_path(self, path_parts: List[str]) -> Optional[str]:
        """在路径组件中查找corner名称
        
        Args:
            path_parts: 路径组件列表
            
        Returns:
            Optional[str]: 找到的corner名称，如果没找到返回None
        """
        # 按优先级尝试匹配corner（从长到短，避免短corner被长corner包含的问题）
        sorted_corners = sorted(self.valid_corners, key=len, reverse=True)
        
        for part in path_parts:
            for corner in sorted_corners:
                # 检查是否包含corner
                if corner in part:
                    # 进一步验证：确保corner是作为独立的组件出现
                    # 例如：test_npg_f1_ssg_cloud 中的 npg_f1_ssg
                    corner_pattern = f'_{corner}'
                    if (part.endswith(corner_pattern) or 
                        f'{corner_pattern}_' in part or 
                        part == corner):
                        return corner
        
        return None
    
    def _find_subsys_in_path(self, path_parts: List[str]) -> Optional[str]:
        """在路径组件中查找子系统名称（标准模式）"""
        for part in path_parts:
            for pattern in self.subsys_patterns:
                if pattern.match(part):
                    return part
        return None
    
    def _find_subsys_in_path_flexible(self, path_parts: List[str]) -> Optional[str]:
        """在路径组件中查找子系统名称（通用模式）
        
        Args:
            path_parts: 路径组件列表
            
        Returns:
            Optional[str]: 找到的子系统名称，如果没找到返回None
        """
        # 首先尝试使用模式匹配
        for part in path_parts:
            for pattern in self.subsys_patterns:
                if pattern.match(part):
                    return part
        
        # 然后尝试在有效子系统列表中查找
        for part in path_parts:
            if part in self.valid_subsys:
                return part
        
        return None

    def _group_by_subsys(self, files: List[RegressionFileInfo]) -> Dict[str, List[RegressionFileInfo]]:
        """按子系统分组"""
        groups = {}
        for file_info in files:
            subsys = file_info.subsys
            if subsys not in groups:
                groups[subsys] = []
            groups[subsys].append(file_info)
        return groups

    def _group_by_corner(self, files: List[RegressionFileInfo]) -> Dict[str, List[RegressionFileInfo]]:
        """按工艺角分组"""
        groups = {}
        for file_info in files:
            corner = file_info.corner_name
            if corner not in groups:
                groups[corner] = []
            groups[corner].append(file_info)
        return groups

    def _group_by_case(self, files: List[RegressionFileInfo]) -> Dict[str, List[RegressionFileInfo]]:
        """按用例分组"""
        groups = {}
        for file_info in files:
            case = file_info.case_name
            if case not in groups:
                groups[case] = []
            groups[case].append(file_info)
        return groups

    def get_scan_statistics(self) -> Dict[str, int]:
        """获取扫描统计信息"""
        return self.scan_stats.copy()


class AsyncRegressionScanner(QThread):
    """异步回归目录扫描器"""

    # 信号定义
    scan_progress = pyqtSignal(int, str)  # 进度百分比, 状态信息
    scan_completed = pyqtSignal(object)   # 扫描完成，返回RegressionScanResult
    scan_failed = pyqtSignal(str)         # 扫描失败，返回错误信息
    file_found = pyqtSignal(object)       # 发现文件，返回RegressionFileInfo

    def __init__(self, regression_root: str, use_standard_structure: bool = True):
        super().__init__()
        self.regression_root = regression_root
        self.scanner = RegressionDirectoryScanner(use_standard_structure)
        self._is_cancelled = False

        # 连接内部扫描器的信号
        self.scanner.scan_progress.connect(self.scan_progress.emit)
        self.scanner.scan_completed.connect(self.scan_completed.emit)
        self.scanner.scan_failed.connect(self.scan_failed.emit)
        self.scanner.file_found.connect(self.file_found.emit)

    def run(self):
        """运行异步扫描"""
        try:
            if self._is_cancelled:
                return

            # 断开内部扫描器的信号连接，避免重复发送
            self.scanner.scan_completed.disconnect()
            self.scanner.scan_failed.disconnect()

            result = self.scanner.scan_regression_directory(
                self.regression_root,
                progress_callback=self._progress_callback
            )

            if not self._is_cancelled:
                self.scan_completed.emit(result)

        except Exception as e:
            if not self._is_cancelled:
                self.scan_failed.emit(str(e))

    def _progress_callback(self, progress: int, message: str):
        """进度回调，检查取消状态"""
        if self._is_cancelled:
            raise InterruptedError("扫描已取消")
        self.scan_progress.emit(progress, message)

    def cancel(self):
        """取消扫描"""
        self._is_cancelled = True
