#!/usr/bin/env python3
"""
验证修复的简单脚本
"""

import os

def main():
    """主函数"""
    print("🔍 验证.xls格式字段解析修复")
    print("=" * 40)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查修复的代码
    parser_file = os.path.join(current_dir, 'parser.py')
    
    if not os.path.exists(parser_file):
        print("❌ parser.py文件不存在")
        return
    
    print("📋 检查修复内容:")
    
    with open(parser_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复点
    fixes_found = []
    
    # 修复1：改进的寄存器行检查
    if 'offset_str = str(offset).strip() if offset is not None else ""' in content:
        fixes_found.append("✅ 寄存器行检查逻辑已修复")
    else:
        fixes_found.append("❌ 寄存器行检查逻辑未修复")
    
    # 修复2：改进的字段名处理
    if 'field_name_raw = row_data.get(\'field_name\')' in content:
        fixes_found.append("✅ 字段名处理逻辑已修复")
    else:
        fixes_found.append("❌ 字段名处理逻辑未修复")
    
    # 修复3：改进的位范围处理
    if 'bit_range_raw = row_data.get(\'bit_range\')' in content:
        fixes_found.append("✅ 位范围处理逻辑已修复")
    else:
        fixes_found.append("❌ 位范围处理逻辑未修复")
    
    # 修复4：改进的读写属性处理
    if 'rw_raw = row_data.get(\'rw\', \'RW\')' in content:
        fixes_found.append("✅ 读写属性处理逻辑已修复")
    else:
        fixes_found.append("❌ 读写属性处理逻辑未修复")
    
    for fix in fixes_found:
        print(f"  {fix}")
    
    # 检查测试文件
    print(f"\n📁 检查测试文件:")
    test_files = [
        'test_register_table.xlsx',
        'test_register_table.xls'
    ]
    
    for filename in test_files:
        file_path = os.path.join(current_dir, filename)
        if os.path.exists(file_path):
            print(f"  ✅ {filename} 存在")
        else:
            print(f"  ❌ {filename} 不存在")
    
    # 总结
    print(f"\n📊 修复总结:")
    successful_fixes = len([f for f in fixes_found if f.startswith("✅")])
    total_fixes = len(fixes_found)
    
    print(f"  修复进度: {successful_fixes}/{total_fixes}")
    
    if successful_fixes == total_fixes:
        print(f"\n🎉 所有修复已完成！")
        print(f"主要改进:")
        print(f"  1. 改进了数据类型处理，正确处理None vs 空字符串")
        print(f"  2. 增强了条件检查逻辑，避免边界情况")
        print(f"  3. 统一了字段数据提取方式")
        print(f"  4. 提高了对不同Excel格式的兼容性")
        print(f"\n现在.xls格式应该能够正确解析寄存器字段信息了！")
    else:
        print(f"\n⚠️  还有 {total_fixes - successful_fixes} 个修复未完成")
    
    print(f"\n🔧 使用方法:")
    print(f"  1. 启动RunSim主程序")
    print(f"  2. 从工具菜单选择'寄存器表格解析器'")
    print(f"  3. 加载.xls格式的寄存器表格文件")
    print(f"  4. 验证是否能正确显示寄存器字段信息")


if __name__ == "__main__":
    main()
