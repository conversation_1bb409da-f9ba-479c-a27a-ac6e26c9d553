#!/usr/bin/env python3
"""
调试真实的用户文件，找出.xls和.xlsx格式差异的根本原因
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False


class XlsWorksheetWrapper:
    """xlrd工作表包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, xlrd_sheet):
        self.sheet = xlrd_sheet
        self.max_row = xlrd_sheet.nrows
        self.max_column = xlrd_sheet.ncols
    
    def cell(self, row, column):
        """获取单元格对象，兼容openpyxl接口"""
        # xlrd使用从0开始的索引，openpyxl使用从1开始的索引
        xlrd_row = row - 1
        xlrd_col = column - 1
        
        if xlrd_row < 0 or xlrd_row >= self.sheet.nrows or xlrd_col < 0 or xlrd_col >= self.sheet.ncols:
            return XlsCellWrapper(None)
        
        try:
            value = self.sheet.cell_value(xlrd_row, xlrd_col)
            return XlsCellWrapper(value)
        except:
            return XlsCellWrapper(None)


class XlsCellWrapper:
    """xlrd单元格包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, value):
        self.value = value


def load_workbook(file_path: str):
    """加载工作簿，自动检测文件格式"""
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.xls':
        if not XLRD_AVAILABLE:
            raise ImportError("需要安装 xlrd 库来读取 .xls 文件: pip install xlrd")
        
        workbook = xlrd.open_workbook(file_path)
        worksheet = workbook.sheet_by_index(0)  # 获取第一个工作表
        return workbook, XlsWorksheetWrapper(worksheet), True
        
    elif file_ext == '.xlsx':
        if not OPENPYXL_AVAILABLE:
            raise ImportError("需要安装 openpyxl 库来读取 .xlsx 文件: pip install openpyxl")
        
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        worksheet = workbook.active
        return workbook, worksheet, False
        
    else:
        raise ValueError(f"不支持的文件格式: {file_ext}，请使用 .xls 或 .xlsx 文件")


def analyze_file_structure(file_path: str):
    """分析文件结构，找出可能的问题"""
    print(f"\n{'='*60}")
    print(f"分析文件结构: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # 加载工作簿
        workbook, worksheet, is_xls = load_workbook(file_path)
        print(f"✅ 成功加载文件，格式: {'XLS' if is_xls else 'XLSX'}")
        print(f"   最大行数: {worksheet.max_row}, 最大列数: {worksheet.max_column}")
        
        # 分析关键列的数据类型
        COLUMN_MAPPING = {
            'offset': 1,        # A列：Offset（偏移地址）
            'reg_name': 2,      # B列：RegName（寄存器名）
            'width': 5,         # E列：Width（寄存器位宽）
            'bit_range': 8,     # H列：Bit（位域，格式[30:0]、[0]等）
            'field_name': 9,    # I列：FieldName（位域名）
            'rw': 10,           # J列：RW（寄存器属性）
            'reset_value': 11,  # K列：ResetValue（位域复位值）
            'set_clear': 12,    # L列：Set/Clear（是否支持set/clear操作）
        }
        
        print(f"\n📊 数据类型分析 (前20行):")
        print(f"{'行号':<4} {'列名':<12} {'原始值':<20} {'类型':<15} {'字符串值':<20} {'是否为空'}")
        print("-" * 90)
        
        type_stats = {}
        empty_stats = {}
        
        for row in range(10, min(30, worksheet.max_row + 1)):  # 分析第10-29行
            for col_name, col_num in COLUMN_MAPPING.items():
                cell_value = worksheet.cell(row=row, column=col_num).value
                value_type = type(cell_value).__name__
                str_value = str(cell_value).strip() if cell_value is not None else ""
                is_empty = not str_value
                
                # 统计类型
                key = f"{col_name}_{value_type}"
                type_stats[key] = type_stats.get(key, 0) + 1
                
                # 统计空值
                empty_key = f"{col_name}_empty"
                if is_empty:
                    empty_stats[empty_key] = empty_stats.get(empty_key, 0) + 1
                
                # 只显示有值的单元格或特殊情况
                if not is_empty or (col_name in ['field_name', 'bit_range'] and row <= 20):
                    print(f"{row:<4} {col_name:<12} {str(cell_value)[:18]:<20} {value_type:<15} '{str_value[:18]}'".ljust(20) + f" {'是' if is_empty else '否'}")
        
        print(f"\n📈 类型统计:")
        for key, count in sorted(type_stats.items()):
            print(f"  {key}: {count}")
        
        print(f"\n📉 空值统计:")
        for key, count in sorted(empty_stats.items()):
            print(f"  {key}: {count}")
        
        # 特别检查字段相关的行
        print(f"\n🔍 字段数据详细分析:")
        field_count = 0
        register_count = 0
        
        for row in range(12, min(50, worksheet.max_row + 1)):  # 检查数据行
            offset = worksheet.cell(row=row, column=1).value
            reg_name = worksheet.cell(row=row, column=2).value
            field_name = worksheet.cell(row=row, column=9).value
            bit_range = worksheet.cell(row=row, column=8).value
            
            offset_str = str(offset).strip() if offset is not None else ""
            reg_name_str = str(reg_name).strip() if reg_name is not None else ""
            field_name_str = str(field_name).strip() if field_name is not None else ""
            bit_range_str = str(bit_range).strip() if bit_range is not None else ""
            
            # 检查是否是寄存器行
            if offset_str and reg_name_str:
                register_count += 1
                print(f"  行{row}: 寄存器 - {reg_name_str} @ {offset_str}")
            
            # 检查是否是字段行
            if field_name_str and bit_range_str:
                field_count += 1
                print(f"  行{row}: 字段 - {field_name_str} [{bit_range_str}]")
                
                # 检查字段名是否为保留字段
                if field_name_str.lower() in ['reserved', 'rsvd', '保留']:
                    print(f"    ⚪ 保留字段，会被跳过")
            
            # 检查可能的问题情况
            if field_name_str and not bit_range_str:
                print(f"  行{row}: ⚠️  有字段名但无位范围: '{field_name_str}'")
            elif bit_range_str and not field_name_str:
                print(f"  行{row}: ⚠️  有位范围但无字段名: '{bit_range_str}'")
        
        print(f"\n📊 统计结果:")
        print(f"  寄存器数量: {register_count}")
        print(f"  字段数量: {field_count}")
        
        # 关闭工作簿
        if hasattr(workbook, 'close'):
            workbook.close()
        
        return register_count, field_count
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0


def find_excel_files():
    """查找Excel文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 查找所有Excel文件
    excel_files = []
    for root, dirs, files in os.walk(current_dir):
        for file in files:
            if file.endswith(('.xlsx', '.xls')):
                excel_files.append(os.path.join(root, file))
    
    return excel_files


def main():
    """主函数"""
    print("🔍 真实文件调试工具")
    print("=" * 40)
    
    # 检查依赖库
    if not OPENPYXL_AVAILABLE:
        print("❌ openpyxl不可用")
        return
    
    if not XLRD_AVAILABLE:
        print("❌ xlrd不可用")
        return
    
    # 查找Excel文件
    excel_files = find_excel_files()
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        print("请将需要调试的.xls和.xlsx文件放在插件目录下")
        return
    
    print(f"📁 找到 {len(excel_files)} 个Excel文件:")
    for file in excel_files:
        rel_path = os.path.relpath(file, os.path.dirname(os.path.abspath(__file__)))
        print(f"  {rel_path}")
    
    # 分析每个文件
    results = {}
    for file_path in excel_files:
        reg_count, field_count = analyze_file_structure(file_path)
        results[file_path] = (reg_count, field_count)
    
    # 比较结果
    print(f"\n{'='*60}")
    print("📊 文件比较结果:")
    print(f"{'='*60}")
    
    for file_path, (reg_count, field_count) in results.items():
        file_name = os.path.basename(file_path)
        print(f"{file_name}: {reg_count} 个寄存器, {field_count} 个字段")
    
    # 查找成对的文件进行比较
    xlsx_files = [f for f in excel_files if f.endswith('.xlsx')]
    xls_files = [f for f in excel_files if f.endswith('.xls')]
    
    for xlsx_file in xlsx_files:
        base_name = os.path.splitext(xlsx_file)[0]
        xls_file = base_name + '.xls'
        
        if xls_file in xls_files:
            xlsx_result = results[xlsx_file]
            xls_result = results[xls_file]
            
            print(f"\n🔄 成对文件比较:")
            print(f"  XLSX: {xlsx_result[0]} 寄存器, {xlsx_result[1]} 字段")
            print(f"  XLS:  {xls_result[0]} 寄存器, {xls_result[1]} 字段")
            
            if xlsx_result == xls_result:
                print("  ✅ 结果一致")
            else:
                print("  ❌ 结果不一致!")
                if xlsx_result[0] != xls_result[0]:
                    print(f"    寄存器数量差异: XLSX={xlsx_result[0]}, XLS={xls_result[0]}")
                if xlsx_result[1] != xls_result[1]:
                    print(f"    字段数量差异: XLSX={xlsx_result[1]}, XLS={xls_result[1]}")


if __name__ == "__main__":
    main()
