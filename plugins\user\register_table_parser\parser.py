"""
Excel寄存器表格解析器 - 新格式

提供Excel文件解析功能，支持新的表格格式：
- 第1-4行：表头信息
- 第5-9行：其他表头信息（忽略）
- 第10行：寄存器表头
- 第11行：Register group（跳过）
- 第12行以后：寄存器信息
"""

import os
import re
from typing import List, Optional, Tuple, Dict, Any
import logging

try:
    import openpyxl
    from openpyxl.worksheet.worksheet import Worksheet
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    Worksheet = None

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False

from .models import (
    HeaderInfo, FieldInfo, RegisterInfo, RegisterTableData,
    ParseError, ValidationError
)


class XlsWorksheetWrapper:
    """xlrd工作表包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, xlrd_sheet):
        self.sheet = xlrd_sheet
        self.max_row = xlrd_sheet.nrows
        self.max_column = xlrd_sheet.ncols
    
    def cell(self, row, column):
        """获取单元格对象，兼容openpyxl接口
        
        Args:
            row: 行号（从1开始）
            column: 列号（从1开始）
            
        Returns:
            单元格包装对象
        """
        # xlrd使用从0开始的索引，openpyxl使用从1开始的索引
        xlrd_row = row - 1
        xlrd_col = column - 1
        
        if xlrd_row < 0 or xlrd_row >= self.sheet.nrows or xlrd_col < 0 or xlrd_col >= self.sheet.ncols:
            return XlsCellWrapper(None)
        
        try:
            value = self.sheet.cell_value(xlrd_row, xlrd_col)
            return XlsCellWrapper(value)
        except:
            return XlsCellWrapper(None)


class XlsCellWrapper:
    """xlrd单元格包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, value):
        self.value = value


class ExcelTableParser:
    """Excel寄存器表格解析器 - 新格式"""
    
    # 新的列映射定义（基于固定位置）
    COLUMN_MAPPING = {
        'offset': 1,        # A列：Offset（偏移地址）
        'reg_name': 2,      # B列：RegName（寄存器名）
        'width': 5,         # E列：Width（寄存器位宽）
        'bit_range': 8,     # H列：Bit（位域，格式[30:0]、[0]等）
        'field_name': 9,    # I列：FieldName（位域名）
        'rw': 10,           # J列：RW（寄存器属性）
        'reset_value': 11,  # K列：ResetValue（位域复位值）
        'set_clear': 12,    # L列：Set/Clear（是否支持set/clear操作）
    }
    
    # 表头行定义
    HEADER_ROW = 10      # 第10行是寄存器表头
    DATA_START_ROW = 12  # 第12行开始是寄存器数据
    
    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)
        self._setup_logging()
        
        if not OPENPYXL_AVAILABLE and not XLRD_AVAILABLE:
            raise ImportError("需要安装 openpyxl 和 xlrd 库: pip install openpyxl xlrd")
        
        # 当前工作表对象，用于统一接口
        self.current_worksheet = None
        self.is_xls_format = False
    
    def _setup_logging(self):
        """设置日志"""
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _load_workbook(self, file_path: str):
        """加载工作簿，自动检测文件格式
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            工作簿对象和工作表对象
        """
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.xls':
            if not XLRD_AVAILABLE:
                raise ImportError("需要安装 xlrd 库来读取 .xls 文件: pip install xlrd")
            
            self.is_xls_format = True
            workbook = xlrd.open_workbook(file_path)
            worksheet = workbook.sheet_by_index(0)  # 获取第一个工作表
            return workbook, XlsWorksheetWrapper(worksheet)
            
        elif file_ext == '.xlsx':
            if not OPENPYXL_AVAILABLE:
                raise ImportError("需要安装 openpyxl 库来读取 .xlsx 文件: pip install openpyxl")
            
            self.is_xls_format = False
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            worksheet = workbook.active
            return workbook, worksheet
            
        else:
            raise ParseError(f"不支持的文件格式: {file_ext}，请使用 .xls 或 .xlsx 文件")

    def parse_register_table(self, file_path: str) -> RegisterTableData:
        """解析寄存器表格Excel文件
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            RegisterTableData: 解析得到的寄存器表格数据
            
        Raises:
            ParseError: 解析错误
            ValidationError: 验证错误
        """
        self.logger.info(f"开始解析Excel文件: {file_path}")
        
        # 检查文件是否存在
        if not os.path.exists(file_path):
            raise ParseError(f"文件不存在: {file_path}")
        
        # 检查文件扩展名
        if not file_path.lower().endswith(('.xlsx', '.xls')):
            raise ParseError(f"不支持的文件格式，请使用Excel文件 (.xlsx 或 .xls)")
        
        try:
            # 加载Excel文件（自动检测格式）
            workbook, worksheet = self._load_workbook(file_path)
            self.current_worksheet = worksheet
            
            # 验证表格格式
            self.validate_table_format(worksheet)
            
            # 提取表头信息
            header_info = self.extract_header_info(worksheet)
            
            # 提取寄存器数据
            registers = self.extract_register_data(worksheet)
            
            # 创建完整的表格数据
            table_data = RegisterTableData(header=header_info, registers=registers)
            
            # 验证数据完整性
            self._validate_table_data(table_data)
            
            self.logger.info(f"解析完成: {len(registers)} 个寄存器")
            return table_data
            
        except Exception as e:
            if isinstance(e, (ParseError, ValidationError)):
                raise
            else:
                raise ParseError(f"解析Excel文件时发生错误: {str(e)}")
        finally:
            try:
                if hasattr(workbook, 'close'):
                    workbook.close()
            except:
                pass
    
    def validate_table_format(self, worksheet) -> bool:
        """验证Excel表格格式
        
        Args:
            worksheet: Excel工作表
            
        Returns:
            bool: 格式是否有效
            
        Raises:
            ParseError: 格式验证失败
        """
        self.logger.info("验证表格格式...")
        
        # 检查工作表是否为空
        if worksheet.max_row < self.DATA_START_ROW:
            raise ParseError(f"表格行数不足，至少需要{self.DATA_START_ROW}行")
        
        # 验证表头行是否存在
        if worksheet.max_row < self.HEADER_ROW:
            raise ParseError(f"未找到表头行（第{self.HEADER_ROW}行）")
        
        # 验证关键列是否有数据
        header_row_data = []
        for col in range(1, 15):  # 检查前14列
            cell_value = worksheet.cell(row=self.HEADER_ROW, column=col).value
            header_row_data.append(str(cell_value).strip() if cell_value else "")
        
        # 检查关键列标题
        expected_headers = {
            1: 'offset',      # A列应该包含Offset相关
            2: 'regname',     # B列应该包含RegName相关
            9: 'fieldname',   # I列应该包含FieldName相关
            10: 'rw',         # J列应该包含RW或R/W相关
        }

        for col_num, expected in expected_headers.items():
            if col_num <= len(header_row_data):
                header_text = header_row_data[col_num - 1].lower()

                # 特殊处理RW列，支持"RW"和"R/W"两种格式
                if expected == 'rw':
                    rw_keywords = ['rw', 'r/w', 'read/write', 'readwrite']
                    if not any(keyword in header_text for keyword in rw_keywords):
                        self.logger.warning(f"第{col_num}列标题可能不正确: '{header_row_data[col_num - 1]}'，期望包含RW或R/W相关内容")
                else:
                    if not any(keyword in header_text for keyword in [expected, expected.replace('name', ''), expected[:4]]):
                        self.logger.warning(f"第{col_num}列标题可能不正确: '{header_row_data[col_num - 1]}'，期望包含'{expected}'")
        
        self.logger.info("表格格式验证通过")
        return True
    
    def extract_header_info(self, worksheet) -> HeaderInfo:
        """提取表头信息
        
        Args:
            worksheet: Excel工作表
            
        Returns:
            HeaderInfo: 表头信息
        """
        self.logger.info("提取表头信息...")
        
        header_info = HeaderInfo()
        
        try:
            # 从前4行提取表头信息
            # 格式：第一行：Project Name | QogirS6
            for row in range(1, 5):  # 行号从1开始
                cell_a = worksheet.cell(row=row, column=1).value
                cell_b = worksheet.cell(row=row, column=2).value
                
                if cell_a and cell_b:
                    label = str(cell_a).strip().lower()
                    value = str(cell_b).strip()
                    
                    # 匹配项目名称
                    if any(keyword in label for keyword in ['project', 'proj', '项目']):
                        header_info.project_name = value
                    # 匹配子系统
                    elif any(keyword in label for keyword in ['sub', 'system', '子系统', '系统']):
                        header_info.sub_system = value
                    # 匹配模块名称
                    elif any(keyword in label for keyword in ['module', 'mod', '模块']):
                        header_info.module_name = value
                    # 匹配基地址
                    elif any(keyword in label for keyword in ['base', 'addr', 'address', '基地址', '地址']):
                        header_info.base_addr = value
            
            # 如果某些字段为空，尝试从其他位置获取
            if not header_info.project_name:
                header_info.project_name = "未知项目"
            if not header_info.sub_system:
                header_info.sub_system = "未知子系统"
            if not header_info.module_name:
                header_info.module_name = "未知模块"
            if not header_info.base_addr:
                header_info.base_addr = "0x0000"
            
        except Exception as e:
            self.logger.warning(f"提取表头信息时出现警告: {str(e)}")
            # 使用默认值
            header_info = HeaderInfo(
                project_name="未知项目",
                sub_system="未知子系统", 
                module_name="未知模块",
                base_addr="0x0000"
            )
        
        self.logger.info(f"表头信息: {header_info}")
        return header_info
    
    def extract_register_data(self, worksheet) -> List[RegisterInfo]:
        """提取寄存器数据
        
        Args:
            worksheet: Excel工作表
            
        Returns:
            List[RegisterInfo]: 寄存器信息列表
        """
        self.logger.info("提取寄存器数据...")
        
        registers = []
        current_register = None
        
        # 从第12行开始逐行解析（跳过第11行的Register group）
        for row_num in range(self.DATA_START_ROW, worksheet.max_row + 1):
            try:
                row_data = self._extract_row_data_by_position(worksheet, row_num)
                
                # 跳过空行
                if not any(str(v).strip() for v in row_data.values() if v is not None):
                    continue
                
                # 跳过"Register group"行或其他分组行
                reg_name = str(row_data.get('reg_name', '')).strip()
                if reg_name.lower() in ['register group', 'group', '']:
                    continue
                
                # 检查是否是新的寄存器行（有偏移地址和寄存器名）
                offset = row_data.get('offset')
                # 改进的条件检查：正确处理None、空字符串和数字0
                offset_str = str(offset).strip() if offset is not None else ""
                reg_name_str = str(reg_name).strip() if reg_name is not None else ""
                if offset_str and reg_name_str:
                    # 保存之前的寄存器
                    if current_register:
                        registers.append(current_register)
                    
                    # 创建新寄存器
                    current_register = RegisterInfo(
                        offset=self._normalize_address(offset),
                        name=reg_name,
                        description="",  # 新格式中没有寄存器描述
                        width=self._parse_width(row_data.get('width', 32)),
                        fields=[]
                    )
                    self.logger.debug(f"创建新寄存器: {reg_name} @ {current_register.offset}")
                
                # 提取字段信息
                field_name_raw = row_data.get('field_name')
                field_name = str(field_name_raw).strip() if field_name_raw is not None else ""
                if current_register and field_name:
                    field = self._create_field_info_new_format(row_data, row_num)
                    if field:
                        current_register.fields.append(field)
                        self.logger.debug(f"添加字段: {field.name} 到寄存器 {current_register.name}")
                
            except Exception as e:
                self.logger.warning(f"解析第 {row_num} 行时出现错误: {str(e)}")
                continue
        
        # 添加最后一个寄存器
        if current_register:
            registers.append(current_register)
        
        # 验证寄存器数据
        self._validate_registers(registers)
        
        self.logger.info(f"提取到 {len(registers)} 个寄存器")
        return registers
    
    def _extract_row_data_by_position(self, worksheet, row_num: int) -> Dict[str, Any]:
        """根据固定位置提取行数据
        
        Args:
            worksheet: Excel工作表
            row_num: 行号
            
        Returns:
            Dict[str, Any]: 行数据
        """
        row_data = {}
        
        for col_type, col_num in self.COLUMN_MAPPING.items():
            cell_value = worksheet.cell(row=row_num, column=col_num).value
            row_data[col_type] = cell_value
        
        return row_data
    
    def _parse_width(self, width_value: Any) -> int:
        """解析寄存器位宽
        
        Args:
            width_value: 位宽值
            
        Returns:
            int: 位宽（默认32）
        """
        if width_value is None:
            return 32
        
        try:
            width_str = str(width_value).strip()
            if width_str.isdigit():
                return int(width_str)
            else:
                # 尝试从字符串中提取数字
                match = re.search(r'\d+', width_str)
                if match:
                    return int(match.group())
        except:
            pass
        
        return 32  # 默认32位
    
    def _create_field_info_new_format(self, row_data: Dict[str, Any], row_num: int) -> Optional[FieldInfo]:
        """根据新格式创建字段信息
        
        Args:
            row_data: 行数据
            row_num: 行号
            
        Returns:
            Optional[FieldInfo]: 字段信息，如果创建失败返回None
        """
        try:
            # 改进的字段名处理
            field_name_raw = row_data.get('field_name')
            field_name = str(field_name_raw).strip() if field_name_raw is not None else ""
            if not field_name:
                return None

            # 跳过保留字段
            if field_name.lower() in ['reserved', 'rsvd', '保留']:
                return None

            # 解析位范围（格式：[30:0]、[0]等）
            bit_range_raw = row_data.get('bit_range')
            bit_range_str = str(bit_range_raw).strip() if bit_range_raw is not None else ""
            bit_range = self._parse_bit_range_brackets(bit_range_str)
            
            if not bit_range:
                self.logger.warning(f"无效的位范围格式: '{bit_range_str}' (行 {row_num})")
                return None

            # 解析读写属性
            rw_raw = row_data.get('rw', 'RW')
            rw_attr = str(rw_raw).strip().upper() if rw_raw is not None else 'RW'
            if not rw_attr:
                rw_attr = 'RW'

            # 解析复位值
            reset_value_raw = row_data.get('reset_value', '0')
            reset_value = str(reset_value_raw).strip() if reset_value_raw is not None else '0'
            if not reset_value:
                reset_value = '0'

            # 检查是否支持set/clear操作
            set_clear_raw = row_data.get('set_clear')
            set_clear = str(set_clear_raw).strip() if set_clear_raw is not None else ""
            description = f"Set/Clear: {set_clear}" if set_clear else ""
            
            field = FieldInfo(
                name=field_name,
                bit_range=bit_range,
                rw_attribute=rw_attr,
                reset_value=reset_value,
                description=description
            )
            
            # 验证字段信息
            self._validate_field_info(field, row_num)
            
            return field
            
        except Exception as e:
            self.logger.warning(f"创建字段信息失败 (行 {row_num}): {str(e)}")
            return None
    
    def _parse_bit_range_brackets(self, bit_range_raw: str) -> str:
        """解析带中括号的位范围格式
        
        Args:
            bit_range_raw: 原始位范围字符串，如"[30:0]"、"[0]"
            
        Returns:
            str: 标准化的位范围字符串，如"30:0"、"0"
        """
        if not bit_range_raw:
            return ""
        
        # 移除中括号
        bit_range = bit_range_raw.strip()
        if bit_range.startswith('[') and bit_range.endswith(']'):
            bit_range = bit_range[1:-1]
        
        # 验证格式
        if ':' in bit_range:
            # 范围格式，如"30:0"
            parts = bit_range.split(':')
            if len(parts) == 2:
                try:
                    high = int(parts[0].strip())
                    low = int(parts[1].strip())
                    if high >= low >= 0:
                        return f"{high}:{low}"
                except ValueError:
                    pass
        else:
            # 单位格式，如"0"
            try:
                bit_num = int(bit_range.strip())
                if bit_num >= 0:
                    return str(bit_num)
            except ValueError:
                pass
        
        return ""
    
    def _validate_field_info(self, field: FieldInfo, row_num: int):
        """验证字段信息
        
        Args:
            field: 字段信息
            row_num: 行号
            
        Raises:
            ValidationError: 验证失败
        """
        # 验证字段名
        if not field.name:
            raise ValidationError(f"字段名不能为空 (行 {row_num})")
        
        # 验证位范围
        if not field.bit_range:
            raise ValidationError(f"位范围不能为空 (行 {row_num})", field.name)
        
        # 验证位范围格式
        if not self._is_valid_bit_range(field.bit_range):
            raise ValidationError(f"位范围格式无效: {field.bit_range} (行 {row_num})", field.name)
        
        # 验证读写属性
        if field.rw_attribute not in ['RW', 'RO', 'WO', '']:
            self.logger.warning(f"未知的读写属性: {field.rw_attribute} (行 {row_num})")
    
    def _is_valid_bit_range(self, bit_range: str) -> bool:
        """验证位范围格式
        
        Args:
            bit_range: 位范围字符串
            
        Returns:
            bool: 格式是否有效
        """
        if not bit_range:
            return False
        
        # 匹配格式: "31:24", "15", "7:0" 等
        pattern = r'^\d+(?::\d+)?$'
        return bool(re.match(pattern, bit_range.strip()))
    
    def _normalize_address(self, address: Any) -> str:
        """标准化地址格式
        
        Args:
            address: 地址值
            
        Returns:
            str: 标准化的地址字符串
        """
        if address is None:
            return "0x0000"
        
        addr_str = str(address).strip()
        
        # 如果已经是十六进制格式
        if addr_str.lower().startswith('0x'):
            return addr_str.upper()
        
        # 处理带下划线的十六进制格式，如"0x6495_1000"
        if '_' in addr_str:
            addr_str = addr_str.replace('_', '')
        
        # 尝试转换为整数然后格式化为十六进制
        try:
            if addr_str.lower().startswith('0x'):
                addr_int = int(addr_str, 16)
            else:
                addr_int = int(addr_str)
            return f"0x{addr_int:04X}"
        except ValueError:
            # 如果转换失败，返回原始字符串
            return addr_str
    
    def _validate_registers(self, registers: List[RegisterInfo]):
        """验证寄存器列表
        
        Args:
            registers: 寄存器列表
            
        Raises:
            ValidationError: 验证失败
        """
        if not registers:
            raise ValidationError("未找到有效的寄存器数据")
        
        # 检查重复的寄存器名称
        register_names = [reg.name for reg in registers]
        duplicates = [name for name in register_names if register_names.count(name) > 1]
        if duplicates:
            raise ValidationError(f"发现重复的寄存器名称: {', '.join(set(duplicates))}")
        
        # 检查重复的偏移地址
        register_offsets = [reg.offset for reg in registers]
        duplicate_offsets = [offset for offset in register_offsets if register_offsets.count(offset) > 1]
        if duplicate_offsets:
            raise ValidationError(f"发现重复的偏移地址: {', '.join(set(duplicate_offsets))}")
        
        # 验证每个寄存器
        for register in registers:
            if not register.fields:
                self.logger.warning(f"寄存器 {register.name} 没有字段")
    
    def _validate_table_data(self, table_data: RegisterTableData):
        """验证完整的表格数据
        
        Args:
            table_data: 表格数据
            
        Raises:
            ValidationError: 验证失败
        """
        if not table_data.is_valid():
            raise ValidationError("表格数据验证失败")
        
        self.logger.info(f"数据验证通过: {table_data}")


def create_parser() -> ExcelTableParser:
    """创建Excel表格解析器实例
    
    Returns:
        ExcelTableParser: 解析器实例
    """
    return ExcelTableParser()