#!/usr/bin/env python3
"""
测试浮点数处理问题
"""

import re

def _parse_width(width_value) -> int:
    """解析寄存器位宽"""
    if width_value is None:
        return 32
    
    try:
        width_str = str(width_value).strip()
        print(f"  原始值: {width_value} (类型: {type(width_value).__name__})")
        print(f"  字符串: '{width_str}'")
        print(f"  isdigit(): {width_str.isdigit()}")
        
        if width_str.isdigit():
            result = int(width_str)
            print(f"  直接转换: {result}")
            return result
        else:
            # 尝试从字符串中提取数字
            match = re.search(r'\d+', width_str)
            if match:
                result = int(match.group())
                print(f"  正则提取: {result}")
                return result
            else:
                print(f"  正则匹配失败")
    except Exception as e:
        print(f"  异常: {e}")
    
    print(f"  返回默认值: 32")
    return 32  # 默认32位


def test_width_parsing():
    """测试位宽解析"""
    print("🔍 测试位宽解析")
    print("=" * 40)
    
    test_values = [
        32,          # int
        32.0,        # float
        "32",        # str
        "32.0",      # str with decimal
        None,        # None
        "",          # empty string
        "abc",       # invalid string
    ]
    
    for value in test_values:
        print(f"\n测试值: {value}")
        result = _parse_width(value)
        print(f"结果: {result}")


def test_string_conversion():
    """测试字符串转换"""
    print("\n🔍 测试字符串转换")
    print("=" * 40)
    
    test_values = [
        32.0,
        "32.0",
        32,
        "32",
    ]
    
    for value in test_values:
        str_value = str(value).strip()
        print(f"值: {value} (类型: {type(value).__name__}) -> 字符串: '{str_value}' -> isdigit(): {str_value.isdigit()}")


if __name__ == "__main__":
    test_string_conversion()
    test_width_parsing()
