{"window": {"maximized": false, "x": 99, "y": 65, "width": 1706, "height": 959}, "base": "apcpu_sys", "block": "ap_sys", "case_files": ["E:/doc/python/runsim/dv/apcpu_sys/bin/case_cfg/apcpu_sys_bus_case.cfg", "E:/doc/python/runsim/dv/top/bin/case_cfg/top_bus_case.cfg", "E:/doc/python/runsim/dv/udtb/apcpu_sys/apcpu_sys_clk/bin/apcpu_sys_clk.cfg", "E:/doc/python/runsim/dv/ap_sys/bin/case_cfg/ap_sys_bus_case.cfg", "E:/doc/python/runsim/dv/udtb/usvp/bin/case_cfg/apcpu_subsys_case.cfg", "E:/doc/python/runsim/dv/udtb/top/top_clk/bin/top_clk.cfg", "E:/doc/python/runsim/dv/udtb/usvp/bin/case_cfg/apcpu_top_case .cfg", "E:/doc/python/runsim/dv/udtb/apcpu_sys/dvfs/bin/dvfs.cfg", "E:/doc/python/runsim/dv/udtb/top/dvfs/bin/dvfs.cfg"], "rundir": "page_test_027_test2", "other_options": "", "fsdb": true, "vwdb": false, "cl": false, "fsdb_file": "", "dump_sva": false, "cov": false, "upf": false, "sim_only": false, "compile_only": false, "dump_mem": "", "wdd": "", "seed": "", "simarg": "", "cfg_def": "", "post": "", "regr_file": "", "fm_checked": false, "bp_server": "", "last_command": "runsim -block top -case top_clk_bist_test", "case": "page_test_027", "bq_server": "", "tag": "", "nt": "", "dashboard": "", "fsdb_checked": true, "vwdb_checked": false, "cl_checked": false, "sva_checked": false, "cov_checked": false, "upf_checked": false, "regr_work": "", "dump_level": ""}