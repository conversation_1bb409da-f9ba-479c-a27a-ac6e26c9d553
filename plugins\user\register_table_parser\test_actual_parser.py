#!/usr/bin/env python3
"""
测试实际的解析器行为
"""

import os
import sys
import logging

# 设置日志级别为INFO以查看详细信息
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def test_with_main_window():
    """使用主窗口的方式测试解析器"""
    print("🔍 测试实际解析器行为")
    print("=" * 50)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 测试文件
    test_files = [
        'template_register_file.xlsx',
        'template_register_file.xls'
    ]
    
    for filename in test_files:
        file_path = os.path.join(current_dir, filename)
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {filename}")
            continue
        
        print(f"\n📁 测试文件: {filename}")
        print(f"   路径: {file_path}")
        
        try:
            # 导入解析器
            from parser import ExcelTableParser
            
            # 创建解析器实例
            parser = ExcelTableParser()
            
            print(f"   🔄 开始解析...")
            
            # 解析文件
            table_data = parser.parse_register_table(file_path)
            
            # 统计结果
            register_count = len(table_data.registers)
            total_field_count = sum(len(reg.fields) for reg in table_data.registers)
            
            print(f"   ✅ 解析完成:")
            print(f"      寄存器数量: {register_count}")
            print(f"      总字段数量: {total_field_count}")
            
            # 显示每个寄存器的详细信息
            for i, register in enumerate(table_data.registers):
                field_count = len(register.fields)
                print(f"      寄存器{i+1}: {register.name} @ {register.offset} ({field_count} 个字段)")
                
                if field_count == 0:
                    print(f"        ⚠️  此寄存器没有字段！")
                else:
                    for j, field in enumerate(register.fields):
                        print(f"        字段{j+1}: {field.name} [{field.bit_range}] {field.rw_attribute}")
            
        except Exception as e:
            print(f"   ❌ 解析失败: {str(e)}")
            import traceback
            traceback.print_exc()


def test_step_by_step():
    """逐步测试解析过程"""
    print(f"\n{'='*50}")
    print("🔍 逐步测试解析过程")
    print(f"{'='*50}")
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    xls_file = os.path.join(current_dir, 'template_register_file.xls')
    
    if not os.path.exists(xls_file):
        print("❌ XLS文件不存在")
        return
    
    try:
        from parser import ExcelTableParser
        
        # 创建解析器实例
        parser = ExcelTableParser()
        
        print("📋 步骤1: 加载工作簿")
        workbook, worksheet = parser._load_workbook(xls_file)
        print(f"   ✅ 成功加载，最大行数: {worksheet.max_row}")
        
        print("\n📋 步骤2: 验证表格格式")
        parser._validate_table_format(worksheet)
        print("   ✅ 表格格式验证通过")
        
        print("\n📋 步骤3: 提取寄存器数据")
        registers = parser.extract_register_data(worksheet)
        print(f"   ✅ 提取到 {len(registers)} 个寄存器")
        
        # 显示详细信息
        for i, register in enumerate(registers):
            field_count = len(register.fields)
            print(f"   寄存器{i+1}: {register.name} @ {register.offset}")
            print(f"     字段数量: {field_count}")
            
            if field_count == 0:
                print(f"     ⚠️  没有字段！")
            else:
                for j, field in enumerate(register.fields):
                    print(f"       字段{j+1}: {field.name} [{field.bit_range}] {field.rw_attribute}")
        
        print("\n📋 步骤4: 验证寄存器")
        parser._validate_registers(registers)
        print("   ✅ 寄存器验证完成")
        
    except Exception as e:
        print(f"❌ 逐步测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 添加当前目录到Python路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    test_with_main_window()
    test_step_by_step()
