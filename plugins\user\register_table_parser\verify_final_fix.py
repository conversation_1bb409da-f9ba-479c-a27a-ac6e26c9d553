#!/usr/bin/env python3
"""
最终验证脚本：确认.xls格式字段解析修复是否生效
"""

import os

def check_parser_fixes():
    """检查解析器修复内容"""
    print("🔍 检查解析器修复内容")
    print("=" * 50)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parser_file = os.path.join(current_dir, 'parser.py')
    
    if not os.path.exists(parser_file):
        print("❌ parser.py文件不存在")
        return False
    
    with open(parser_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键修复点
    fixes = [
        {
            'name': '寄存器行检查逻辑修复',
            'pattern': 'offset_str = str(offset).strip() if offset is not None else ""',
            'found': False
        },
        {
            'name': '字段名处理修复',
            'pattern': 'field_name_raw = row_data.get(\'field_name\')',
            'found': False
        },
        {
            'name': '位范围处理修复',
            'pattern': 'bit_range_raw = row_data.get(\'bit_range\')',
            'found': False
        },
        {
            'name': '读写属性处理修复',
            'pattern': 'rw_raw = row_data.get(\'rw\', \'RW\')',
            'found': False
        },
        {
            'name': '表头验证增强',
            'pattern': 'rw_keywords = [\'rw\', \'r/w\', \'read/write\', \'readwrite\']',
            'found': False
        }
    ]
    
    # 检查每个修复点
    for fix in fixes:
        if fix['pattern'] in content:
            fix['found'] = True
            print(f"  ✅ {fix['name']}")
        else:
            print(f"  ❌ {fix['name']}")
    
    # 统计结果
    found_count = sum(1 for fix in fixes if fix['found'])
    total_count = len(fixes)
    
    print(f"\n📊 修复检查结果: {found_count}/{total_count}")
    
    if found_count == total_count:
        print("🎉 所有修复都已正确应用！")
        return True
    else:
        print(f"⚠️  还有 {total_count - found_count} 个修复未完成")
        return False


def check_test_files():
    """检查测试文件"""
    print(f"\n📁 检查测试文件")
    print("=" * 30)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    test_files = [
        'template_register_file.xlsx',
        'template_register_file.xls',
        'debug_field_issue.py'
    ]
    
    all_exist = True
    
    for filename in test_files:
        file_path = os.path.join(current_dir, filename)
        if os.path.exists(file_path):
            print(f"  ✅ {filename}")
        else:
            print(f"  ❌ {filename}")
            all_exist = False
    
    return all_exist


def provide_next_steps():
    """提供下一步操作指导"""
    print(f"\n🔧 下一步操作指导")
    print("=" * 30)
    
    print("1. 重启RunSim主程序（确保加载最新的修复代码）")
    print("2. 从工具菜单选择'寄存器表格解析器'")
    print("3. 加载.xls格式的寄存器表格文件")
    print("4. 观察终端日志，确认不再显示'寄存器 XXX 没有字段'警告")
    print("5. 在界面中验证寄存器字段信息是否正确显示")
    
    print(f"\n🧪 可选验证步骤:")
    print("运行调试脚本验证修复效果：")
    print("  python debug_field_issue.py")


def main():
    """主函数"""
    print("🔍 .xls格式字段解析修复 - 最终验证")
    print("=" * 60)
    
    # 检查修复内容
    fixes_ok = check_parser_fixes()
    
    # 检查测试文件
    files_ok = check_test_files()
    
    # 总结
    print(f"\n{'='*60}")
    print("📊 验证总结")
    print(f"{'='*60}")
    
    if fixes_ok and files_ok:
        print("✅ 所有修复已完成，文件齐全")
        print("🎉 .xls格式字段解析问题已修复！")
        
        print(f"\n🔧 修复内容总结:")
        print("  1. 改进了数据类型处理（None vs 空字符串）")
        print("  2. 增强了条件检查逻辑（避免边界情况）")
        print("  3. 统一了字段数据提取方式")
        print("  4. 支持'RW'和'R/W'两种表头格式")
        print("  5. 提高了对不同Excel格式的兼容性")
        
    else:
        print("⚠️  验证发现问题:")
        if not fixes_ok:
            print("  - 部分修复未正确应用")
        if not files_ok:
            print("  - 缺少必要的测试文件")
    
    # 提供操作指导
    provide_next_steps()


if __name__ == "__main__":
    main()
