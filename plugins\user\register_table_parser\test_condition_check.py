#!/usr/bin/env python3
"""
测试条件检查的差异
"""

def test_condition_checks():
    """测试不同值的条件检查"""
    print("🔍 测试条件检查")
    print("=" * 40)
    
    # 模拟不同的offset和reg_name值
    test_cases = [
        # (offset, reg_name, 描述)
        ("0x0000", "TEST_REG", "正常情况"),
        (None, "TEST_REG", "offset为None"),
        ("", "TEST_REG", "offset为空字符串"),
        ("0x0000", None, "reg_name为None"),
        ("0x0000", "", "reg_name为空字符串"),
        (None, None, "都为None"),
        ("", "", "都为空字符串"),
        (0, "TEST_REG", "offset为0"),
        ("0", "TEST_REG", "offset为字符串0"),
    ]
    
    for offset, reg_name, desc in test_cases:
        # 模拟原始条件检查
        condition1 = offset and reg_name
        
        # 改进的条件检查
        offset_str = str(offset).strip() if offset is not None else ""
        reg_name_str = str(reg_name).strip() if reg_name is not None else ""
        condition2 = offset_str and reg_name_str
        
        print(f"\n{desc}:")
        print(f"  offset: {offset} (类型: {type(offset).__name__})")
        print(f"  reg_name: {reg_name} (类型: {type(reg_name).__name__})")
        print(f"  原始条件 (offset and reg_name): {condition1}")
        print(f"  改进条件 (offset_str and reg_name_str): {condition2}")
        
        if condition1 != condition2:
            print(f"  ⚠️  条件结果不一致!")


if __name__ == "__main__":
    test_condition_checks()
