#!/usr/bin/env python3
"""
寄存器表格解析器插件依赖安装脚本

该脚本用于安装寄存器表格解析器插件所需的依赖库：
- openpyxl: 用于读取 .xlsx 格式的Excel文件
- xlrd: 用于读取 .xls 格式的Excel文件
"""

import subprocess
import sys
import importlib


def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_package(package_name):
    """安装指定的包"""
    try:
        print(f"正在安装 {package_name}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✓ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package_name} 安装失败: {e}")
        return False


def install_register_parser_dependencies():
    """安装寄存器表格解析器依赖"""
    print("=" * 60)
    print("寄存器表格解析器插件 - 依赖安装脚本")
    print("=" * 60)
    
    # 需要安装的依赖包
    dependencies = [
        ("openpyxl", "openpyxl"),  # (import_name, package_name)
        ("xlrd", "xlrd"),
    ]
    
    installed_count = 0
    failed_count = 0
    
    for import_name, package_name in dependencies:
        print(f"\n检查 {package_name}...")
        
        if check_package_installed(import_name):
            print(f"✓ {package_name} 已安装")
            installed_count += 1
        else:
            print(f"✗ {package_name} 未安装")
            if install_package(package_name):
                installed_count += 1
            else:
                failed_count += 1
    
    print("\n" + "=" * 60)
    print("安装结果:")
    print(f"✓ 成功: {installed_count} 个依赖")
    if failed_count > 0:
        print(f"✗ 失败: {failed_count} 个依赖")
    print("=" * 60)
    
    if failed_count == 0:
        print("\n🎉 所有依赖安装完成！")
        print("\n现在可以使用寄存器表格解析器插件了：")
        print("- 支持 .xlsx 格式的Excel文件（使用 openpyxl）")
        print("- 支持 .xls 格式的Excel文件（使用 xlrd）")
        return True
    else:
        print(f"\n❌ 有 {failed_count} 个依赖安装失败")
        print("请检查网络连接或手动安装失败的依赖")
        return False


def main():
    """主函数"""
    try:
        success = install_register_parser_dependencies()
        
        if success:
            print("\n按任意键退出...")
            input()
            sys.exit(0)
        else:
            print("\n按任意键退出...")
            input()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
        sys.exit(1)
    except Exception as e:
        print(f"\n安装过程中发生错误: {e}")
        print("按任意键退出...")
        input()
        sys.exit(1)


if __name__ == "__main__":
    main()