#!/usr/bin/env python3
"""
测试RW表头差异修复
"""

import os
import sys
import logging

# 设置日志级别
logging.basicConfig(level=logging.INFO)

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 动态导入解析器模块
import importlib.util

def load_parser_module():
    """动态加载解析器模块"""
    parser_path = os.path.join(os.path.dirname(__file__), 'parser.py')
    spec = importlib.util.spec_from_file_location("parser", parser_path)
    parser_module = importlib.util.module_from_spec(spec)
    
    # 加载models模块
    models_path = os.path.join(os.path.dirname(__file__), 'models.py')
    models_spec = importlib.util.spec_from_file_location("models", models_path)
    models_module = importlib.util.module_from_spec(models_spec)
    models_spec.loader.exec_module(models_module)
    
    # 将models模块添加到sys.modules中
    sys.modules['models'] = models_module
    
    # 执行解析器模块
    spec.loader.exec_module(parser_module)
    return parser_module

def test_rw_header_parsing():
    """测试RW表头解析"""
    print("🔍 测试RW表头差异修复")
    print("=" * 50)
    
    try:
        # 加载解析器模块
        parser_module = load_parser_module()
        ExcelTableParser = parser_module.ExcelTableParser
        
        # 创建解析器实例
        parser = ExcelTableParser()
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 测试文件
        test_files = [
            ('template_register_file.xlsx', 'XLSX模板文件 (RW表头)'),
            ('template_register_file.xls', 'XLS模板文件 (R/W表头)'),
        ]
        
        results = {}
        
        for filename, description in test_files:
            file_path = os.path.join(current_dir, filename)
            
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {filename}")
                continue
            
            print(f"\n📁 测试 {description}")
            print(f"   文件: {filename}")
            
            try:
                # 解析文件
                table_data = parser.parse_register_table(file_path)
                
                # 统计结果
                register_count = len(table_data.registers)
                total_field_count = sum(len(reg.fields) for reg in table_data.registers)
                non_reserved_field_count = sum(
                    len([f for f in reg.fields if not f.name.lower().startswith('reserved')])
                    for reg in table_data.registers
                )
                
                results[filename] = {
                    'registers': register_count,
                    'total_fields': total_field_count,
                    'non_reserved_fields': non_reserved_field_count,
                    'success': True
                }
                
                print(f"   ✅ 解析成功:")
                print(f"      寄存器数量: {register_count}")
                print(f"      总字段数量: {total_field_count}")
                print(f"      非保留字段: {non_reserved_field_count}")
                
                # 显示前几个寄存器的详细信息
                for i, register in enumerate(table_data.registers[:3]):  # 只显示前3个
                    print(f"      寄存器{i+1}: {register.name} @ {register.offset}")
                    field_count = len(register.fields)
                    non_reserved_count = len([f for f in register.fields if not f.name.lower().startswith('reserved')])
                    print(f"        字段: {field_count}个 (非保留: {non_reserved_count}个)")
                    
                    # 显示前几个字段
                    for j, field in enumerate(register.fields[:2]):  # 只显示前2个字段
                        field_type = "保留" if field.name.lower().startswith('reserved') else "普通"
                        print(f"          {field.name} [{field.bit_range}] {field.rw_attribute} ({field_type})")
                
                if len(table_data.registers) > 3:
                    print(f"      ... 还有 {len(table_data.registers) - 3} 个寄存器")
                
            except Exception as e:
                print(f"   ❌ 解析失败: {str(e)}")
                results[filename] = {
                    'registers': 0,
                    'total_fields': 0,
                    'non_reserved_fields': 0,
                    'success': False,
                    'error': str(e)
                }
        
        # 比较结果
        print(f"\n{'='*50}")
        print("📊 解析结果比较:")
        print(f"{'='*50}")
        
        xlsx_result = results.get('template_register_file.xlsx')
        xls_result = results.get('template_register_file.xls')
        
        if xlsx_result and xls_result:
            if xlsx_result['success'] and xls_result['success']:
                print(f"XLSX文件 (RW表头):  {xlsx_result['registers']} 寄存器, {xlsx_result['non_reserved_fields']} 字段")
                print(f"XLS文件 (R/W表头):  {xls_result['registers']} 寄存器, {xls_result['non_reserved_fields']} 字段")
                
                if (xlsx_result['registers'] == xls_result['registers'] and 
                    xlsx_result['non_reserved_fields'] == xls_result['non_reserved_fields']):
                    print("\n🎉 修复成功！RW表头差异问题已解决！")
                    print("✅ 解析器现在能正确处理'RW'和'R/W'两种表头格式")
                    print("✅ .xls和.xlsx格式解析结果一致")
                else:
                    print("\n⚠️  解析结果仍有差异:")
                    if xlsx_result['registers'] != xls_result['registers']:
                        print(f"   寄存器数量: XLSX={xlsx_result['registers']}, XLS={xls_result['registers']}")
                    if xlsx_result['non_reserved_fields'] != xls_result['non_reserved_fields']:
                        print(f"   字段数量: XLSX={xlsx_result['non_reserved_fields']}, XLS={xls_result['non_reserved_fields']}")
                    
                    # 如果字段数量仍然为0，说明还有其他问题
                    if xls_result['non_reserved_fields'] == 0:
                        print("\n🔍 XLS文件字段数量为0，可能的原因:")
                        print("   1. 表头识别问题")
                        print("   2. 数据类型处理问题")
                        print("   3. 条件检查逻辑问题")
            else:
                print("❌ 部分文件解析失败")
                if not xlsx_result['success']:
                    print(f"   XLSX文件错误: {xlsx_result.get('error', '未知错误')}")
                if not xls_result['success']:
                    print(f"   XLS文件错误: {xls_result.get('error', '未知错误')}")
        else:
            print("❌ 缺少测试文件")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_rw_header_parsing()
