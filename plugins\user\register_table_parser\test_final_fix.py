#!/usr/bin/env python3
"""
最终测试：验证修复是否解决了.xls格式字段解析问题
"""

import os
import sys
import logging

# 添加路径以便导入模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志级别为WARNING，减少输出
logging.basicConfig(level=logging.WARNING)

# 动态导入解析器模块
import importlib.util

def load_parser_module():
    """动态加载解析器模块"""
    parser_path = os.path.join(os.path.dirname(__file__), 'parser.py')
    spec = importlib.util.spec_from_file_location("parser", parser_path)
    parser_module = importlib.util.module_from_spec(spec)
    
    # 加载models模块
    models_path = os.path.join(os.path.dirname(__file__), 'models.py')
    models_spec = importlib.util.spec_from_file_location("models", models_path)
    models_module = importlib.util.module_from_spec(models_spec)
    models_spec.loader.exec_module(models_module)
    
    # 将models模块添加到sys.modules中
    sys.modules['models'] = models_module
    
    # 执行解析器模块
    spec.loader.exec_module(parser_module)
    return parser_module

def test_parser_with_files():
    """使用实际的解析器测试文件"""
    print("🔍 最终测试：验证.xls格式字段解析修复")
    print("=" * 50)
    
    try:
        # 加载解析器模块
        parser_module = load_parser_module()
        ExcelTableParser = parser_module.ExcelTableParser
        
        # 创建解析器实例
        parser = ExcelTableParser()
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 测试文件
        test_files = [
            ('test_register_table.xlsx', 'XLSX测试文件'),
            ('test_register_table.xls', 'XLS测试文件'),
        ]
        
        results = {}
        
        for filename, description in test_files:
            file_path = os.path.join(current_dir, filename)
            
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {filename}")
                continue
            
            print(f"\n📁 测试 {description}: {filename}")
            
            try:
                # 解析文件
                table_data = parser.parse_register_table(file_path)
                
                # 统计结果
                register_count = len(table_data.registers)
                field_count = sum(len(reg.fields) for reg in table_data.registers)
                non_reserved_field_count = sum(
                    len([f for f in reg.fields if not f.name.lower().startswith('reserved')])
                    for reg in table_data.registers
                )
                
                results[filename] = {
                    'registers': register_count,
                    'total_fields': field_count,
                    'non_reserved_fields': non_reserved_field_count,
                    'success': True
                }
                
                print(f"  ✅ 解析成功:")
                print(f"     寄存器数量: {register_count}")
                print(f"     总字段数量: {field_count}")
                print(f"     非保留字段: {non_reserved_field_count}")
                
                # 显示详细信息
                for i, register in enumerate(table_data.registers):
                    print(f"     寄存器{i+1}: {register.name} @ {register.offset}")
                    for j, field in enumerate(register.fields):
                        field_type = "保留" if field.name.lower().startswith('reserved') else "普通"
                        print(f"       字段{j+1}: {field.name} [{field.bit_range}] ({field_type})")
                
            except Exception as e:
                print(f"  ❌ 解析失败: {str(e)}")
                results[filename] = {
                    'registers': 0,
                    'total_fields': 0,
                    'non_reserved_fields': 0,
                    'success': False,
                    'error': str(e)
                }
        
        # 比较结果
        print(f"\n{'='*50}")
        print("📊 解析结果比较:")
        print(f"{'='*50}")
        
        xlsx_result = results.get('test_register_table.xlsx')
        xls_result = results.get('test_register_table.xls')
        
        if xlsx_result and xls_result:
            if xlsx_result['success'] and xls_result['success']:
                print(f"XLSX文件: {xlsx_result['registers']} 寄存器, {xlsx_result['non_reserved_fields']} 字段")
                print(f"XLS文件:  {xls_result['registers']} 寄存器, {xls_result['non_reserved_fields']} 字段")
                
                if (xlsx_result['registers'] == xls_result['registers'] and 
                    xlsx_result['non_reserved_fields'] == xls_result['non_reserved_fields']):
                    print("\n🎉 修复成功！.xls和.xlsx格式解析结果一致！")
                    print("✅ .xls格式现在能够正确解析寄存器字段信息")
                else:
                    print("\n⚠️  解析结果仍有差异:")
                    if xlsx_result['registers'] != xls_result['registers']:
                        print(f"   寄存器数量: XLSX={xlsx_result['registers']}, XLS={xls_result['registers']}")
                    if xlsx_result['non_reserved_fields'] != xls_result['non_reserved_fields']:
                        print(f"   字段数量: XLSX={xlsx_result['non_reserved_fields']}, XLS={xls_result['non_reserved_fields']}")
            else:
                print("❌ 部分文件解析失败")
                if not xlsx_result['success']:
                    print(f"   XLSX文件错误: {xlsx_result.get('error', '未知错误')}")
                if not xls_result['success']:
                    print(f"   XLS文件错误: {xls_result.get('error', '未知错误')}")
        else:
            print("❌ 缺少测试文件")
        
        # 测试模板文件（如果存在）
        template_files = [
            ('template_register_file.xlsx', 'XLSX模板文件'),
            ('template_register_file.xls', 'XLS模板文件'),
        ]
        
        template_results = {}
        
        print(f"\n📋 模板文件测试:")
        for filename, description in template_files:
            file_path = os.path.join(current_dir, filename)
            
            if os.path.exists(file_path):
                try:
                    table_data = parser.parse_register_table(file_path)
                    register_count = len(table_data.registers)
                    field_count = sum(
                        len([f for f in reg.fields if not f.name.lower().startswith('reserved')])
                        for reg in table_data.registers
                    )
                    template_results[filename] = (register_count, field_count)
                    print(f"  {description}: {register_count} 寄存器, {field_count} 字段")
                except Exception as e:
                    print(f"  {description}: 解析失败 - {str(e)}")
        
        if len(template_results) == 2:
            xlsx_template = template_results.get('template_register_file.xlsx')
            xls_template = template_results.get('template_register_file.xls')
            
            if xlsx_template and xls_template:
                if xlsx_template == xls_template:
                    print("  ✅ 模板文件解析结果一致")
                else:
                    print(f"  ⚠️  模板文件仍有差异: XLSX={xlsx_template}, XLS={xls_template}")
                    print("     这可能是由于文件本身的内容差异造成的")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_parser_with_files()
