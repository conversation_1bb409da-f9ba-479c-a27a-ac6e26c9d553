#!/usr/bin/env python3
"""
直接测试解析器，不依赖相对导入
"""

import os
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.DEBUG)

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False


class XlsWorksheetWrapper:
    """xlrd工作表包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, xlrd_sheet):
        self.sheet = xlrd_sheet
        self.max_row = xlrd_sheet.nrows
        self.max_column = xlrd_sheet.ncols
    
    def cell(self, row, column):
        """获取单元格对象，兼容openpyxl接口"""
        # xlrd使用从0开始的索引，openpyxl使用从1开始的索引
        xlrd_row = row - 1
        xlrd_col = column - 1
        
        if xlrd_row < 0 or xlrd_row >= self.sheet.nrows or xlrd_col < 0 or xlrd_col >= self.sheet.ncols:
            return XlsCellWrapper(None)
        
        try:
            value = self.sheet.cell_value(xlrd_row, xlrd_col)
            return XlsCellWrapper(value)
        except:
            return XlsCellWrapper(None)


class XlsCellWrapper:
    """xlrd单元格包装器，提供与openpyxl类似的接口"""
    
    def __init__(self, value):
        self.value = value


def load_workbook(file_path: str):
    """加载工作簿，自动检测文件格式"""
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.xls':
        if not XLRD_AVAILABLE:
            raise ImportError("需要安装 xlrd 库来读取 .xls 文件: pip install xlrd")
        
        workbook = xlrd.open_workbook(file_path)
        worksheet = workbook.sheet_by_index(0)  # 获取第一个工作表
        return workbook, XlsWorksheetWrapper(worksheet), True
        
    elif file_ext == '.xlsx':
        if not OPENPYXL_AVAILABLE:
            raise ImportError("需要安装 openpyxl 库来读取 .xlsx 文件: pip install openpyxl")
        
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        worksheet = workbook.active
        return workbook, worksheet, False
        
    else:
        raise ValueError(f"不支持的文件格式: {file_ext}，请使用 .xls 或 .xlsx 文件")


def extract_row_data_by_position(worksheet, row_num: int):
    """根据固定位置提取行数据"""
    COLUMN_MAPPING = {
        'offset': 1,        # A列：Offset（偏移地址）
        'reg_name': 2,      # B列：RegName（寄存器名）
        'width': 5,         # E列：Width（寄存器位宽）
        'bit_range': 8,     # H列：Bit（位域，格式[30:0]、[0]等）
        'field_name': 9,    # I列：FieldName（位域名）
        'rw': 10,           # J列：RW（寄存器属性）
        'reset_value': 11,  # K列：ResetValue（位域复位值）
        'set_clear': 12,    # L列：Set/Clear（是否支持set/clear操作）
    }
    
    row_data = {}
    
    for col_type, col_num in COLUMN_MAPPING.items():
        cell_value = worksheet.cell(row=row_num, column=col_num).value
        row_data[col_type] = cell_value
    
    return row_data


def parse_bit_range_brackets(bit_range_raw: str) -> str:
    """解析带中括号的位范围格式"""
    if not bit_range_raw:
        return ""
    
    # 移除中括号
    bit_range = bit_range_raw.strip()
    if bit_range.startswith('[') and bit_range.endswith(']'):
        bit_range = bit_range[1:-1]
    
    # 验证格式
    if ':' in bit_range:
        # 范围格式，如"30:0"
        parts = bit_range.split(':')
        if len(parts) == 2:
            try:
                high = int(parts[0].strip())
                low = int(parts[1].strip())
                if high >= low >= 0:
                    return f"{high}:{low}"
            except ValueError:
                pass
    else:
        # 单位格式，如"0"
        try:
            bit_num = int(bit_range.strip())
            if bit_num >= 0:
                return str(bit_num)
        except ValueError:
            pass
    
    return ""


def test_field_creation(row_data, row_num):
    """测试字段创建过程"""
    print(f"\n  🔍 测试字段创建 (行 {row_num}):")
    
    field_name = str(row_data.get('field_name', '')).strip()
    print(f"    字段名: '{field_name}' (是否为空: {not field_name})")
    
    if not field_name:
        print("    ❌ 字段名为空，跳过")
        return None
    
    # 跳过保留字段
    if field_name.lower() in ['reserved', 'rsvd', '保留']:
        print("    ⚪ 保留字段，跳过")
        return None
    
    # 解析位范围
    bit_range_raw = str(row_data.get('bit_range', '')).strip()
    bit_range = parse_bit_range_brackets(bit_range_raw)
    print(f"    位范围原始: '{bit_range_raw}' -> 解析后: '{bit_range}'")
    
    if not bit_range:
        print(f"    ❌ 无效的位范围格式: '{bit_range_raw}'")
        return None
    
    # 解析读写属性
    rw_attr = str(row_data.get('rw', 'RW')).strip().upper()
    if not rw_attr:
        rw_attr = 'RW'
    
    # 解析复位值
    reset_value = str(row_data.get('reset_value', '0')).strip()
    if not reset_value:
        reset_value = '0'
    
    print(f"    ✅ 字段创建成功: {field_name} [{bit_range}] {rw_attr} = {reset_value}")
    return {
        'name': field_name,
        'bit_range': bit_range,
        'rw_attribute': rw_attr,
        'reset_value': reset_value
    }


def test_file_parsing(file_path: str):
    """测试文件解析"""
    print(f"\n{'='*60}")
    print(f"测试文件解析: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    try:
        # 加载工作簿
        workbook, worksheet, is_xls = load_workbook(file_path)
        print(f"✅ 成功加载文件，格式: {'XLS' if is_xls else 'XLSX'}")
        print(f"   最大行数: {worksheet.max_row}, 最大列数: {worksheet.max_column}")
        
        # 测试寄存器数据提取
        DATA_START_ROW = 12
        registers = []
        current_register = None
        
        print(f"\n📋 逐行解析数据 (从第{DATA_START_ROW}行开始):")
        
        for row_num in range(DATA_START_ROW, min(DATA_START_ROW + 10, worksheet.max_row + 1)):
            print(f"\n第{row_num}行:")
            
            row_data = extract_row_data_by_position(worksheet, row_num)
            
            # 显示原始数据
            for key, value in row_data.items():
                print(f"  {key}: '{value}' (类型: {type(value).__name__})")
            
            # 跳过空行
            if not any(str(v).strip() for v in row_data.values() if v is not None):
                print("  ⚪ 空行，跳过")
                continue
            
            # 检查是否是新的寄存器行
            offset = row_data.get('offset')
            reg_name = str(row_data.get('reg_name', '')).strip()
            
            if offset and reg_name:
                # 保存之前的寄存器
                if current_register:
                    registers.append(current_register)
                    print(f"  📝 保存寄存器: {current_register['name']} (字段数: {len(current_register['fields'])})")
                
                # 创建新寄存器
                current_register = {
                    'offset': str(offset),
                    'name': reg_name,
                    'fields': []
                }
                print(f"  🆕 创建新寄存器: {reg_name} @ {offset}")
            
            # 提取字段信息
            field = test_field_creation(row_data, row_num)
            if current_register and field:
                current_register['fields'].append(field)
                print(f"  ➕ 添加字段到寄存器 {current_register['name']}")
        
        # 添加最后一个寄存器
        if current_register:
            registers.append(current_register)
            print(f"\n📝 保存最后一个寄存器: {current_register['name']} (字段数: {len(current_register['fields'])})")
        
        # 显示结果
        print(f"\n📊 解析结果:")
        print(f"  总寄存器数: {len(registers)}")
        
        for i, reg in enumerate(registers):
            print(f"  寄存器{i+1}: {reg['name']} @ {reg['offset']}")
            print(f"    字段数量: {len(reg['fields'])}")
            for j, field in enumerate(reg['fields']):
                print(f"      字段{j+1}: {field['name']} [{field['bit_range']}] {field['rw_attribute']}")
        
        # 关闭工作簿
        if hasattr(workbook, 'close'):
            workbook.close()
        
        return len(registers), sum(len(reg['fields']) for reg in registers)
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return 0, 0


def main():
    """主函数"""
    print("🔍 直接测试解析器")
    print("=" * 40)
    
    # 检查依赖库
    if not OPENPYXL_AVAILABLE:
        print("❌ openpyxl不可用")
        return
    
    if not XLRD_AVAILABLE:
        print("❌ xlrd不可用")
        return
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    xlsx_file = os.path.join(current_dir, 'test_register_table.xlsx')
    xls_file = os.path.join(current_dir, 'test_register_table.xls')
    
    if not os.path.exists(xlsx_file) or not os.path.exists(xls_file):
        print("❌ 测试文件不存在，请先运行 create_test_files.py")
        return
    
    # 测试两个文件
    xlsx_regs, xlsx_fields = test_file_parsing(xlsx_file)
    xls_regs, xls_fields = test_file_parsing(xls_file)
    
    # 比较结果
    print(f"\n{'='*60}")
    print("📊 解析结果比较:")
    print(f"{'='*60}")
    print(f"XLSX文件: {xlsx_regs} 个寄存器, {xlsx_fields} 个字段")
    print(f"XLS文件:  {xls_regs} 个寄存器, {xls_fields} 个字段")
    
    if xlsx_regs == xls_regs and xlsx_fields == xls_fields:
        print("✅ 解析结果一致!")
    else:
        print("❌ 解析结果不一致!")
        if xlsx_regs != xls_regs:
            print(f"  寄存器数量差异: XLSX={xlsx_regs}, XLS={xls_regs}")
        if xlsx_fields != xls_fields:
            print(f"  字段数量差异: XLSX={xlsx_fields}, XLS={xls_fields}")


if __name__ == "__main__":
    main()
