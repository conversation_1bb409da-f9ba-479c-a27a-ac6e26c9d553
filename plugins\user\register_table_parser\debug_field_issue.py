#!/usr/bin/env python3
"""
调试字段解析问题
"""

import os

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False


class XlsWorksheetWrapper:
    """xlrd工作表包装器"""
    
    def __init__(self, xlrd_sheet):
        self.sheet = xlrd_sheet
        self.max_row = xlrd_sheet.nrows
        self.max_column = xlrd_sheet.ncols
    
    def cell(self, row, column):
        """获取单元格对象"""
        xlrd_row = row - 1
        xlrd_col = column - 1
        
        if xlrd_row < 0 or xlrd_row >= self.sheet.nrows or xlrd_col < 0 or xlrd_col >= self.sheet.ncols:
            return XlsCellWrapper(None)
        
        try:
            value = self.sheet.cell_value(xlrd_row, xlrd_col)
            return XlsCellWrapper(value)
        except:
            return XlsCellWrapper(None)


class XlsCellWrapper:
    """xlrd单元格包装器"""
    
    def __init__(self, value):
        self.value = value


def load_workbook(file_path: str):
    """加载工作簿"""
    file_ext = os.path.splitext(file_path)[1].lower()
    
    if file_ext == '.xls':
        workbook = xlrd.open_workbook(file_path)
        worksheet = workbook.sheet_by_index(0)
        return workbook, XlsWorksheetWrapper(worksheet), True
    elif file_ext == '.xlsx':
        workbook = openpyxl.load_workbook(file_path, data_only=True)
        worksheet = workbook.active
        return workbook, worksheet, False
    else:
        raise ValueError(f"不支持的文件格式: {file_ext}")


def debug_field_parsing(file_path: str):
    """调试字段解析"""
    print(f"\n{'='*60}")
    print(f"调试字段解析: {os.path.basename(file_path)}")
    print(f"{'='*60}")
    
    # 列映射
    COLUMN_MAPPING = {
        'offset': 1,        # A列：Offset
        'reg_name': 2,      # B列：RegName
        'width': 5,         # E列：Width
        'bit_range': 8,     # H列：Bit
        'field_name': 9,    # I列：FieldName
        'rw': 10,           # J列：RW
        'reset_value': 11,  # K列：ResetValue
        'set_clear': 12,    # L列：Set/Clear
    }
    
    try:
        workbook, worksheet, is_xls = load_workbook(file_path)
        print(f"✅ 成功加载文件，格式: {'XLS' if is_xls else 'XLSX'}")
        
        # 检查表头
        print(f"\n📋 表头检查 (第10行):")
        for col_name, col_num in COLUMN_MAPPING.items():
            cell_value = worksheet.cell(row=10, column=col_num).value
            print(f"  {col_name} (列{col_num}): '{cell_value}'")
        
        # 模拟字段解析逻辑
        print(f"\n🔍 字段解析模拟 (第12-16行):")
        
        current_register = None
        field_count = 0
        
        for row_num in range(12, 17):  # 检查前几行
            print(f"\n第{row_num}行:")
            
            # 提取行数据
            row_data = {}
            for col_type, col_num in COLUMN_MAPPING.items():
                cell_value = worksheet.cell(row=row_num, column=col_num).value
                row_data[col_type] = cell_value
                print(f"  {col_type}: '{cell_value}' (类型: {type(cell_value).__name__})")
            
            # 检查是否是新的寄存器行
            offset = row_data.get('offset')
            reg_name = row_data.get('reg_name')
            
            # 使用修复后的逻辑
            offset_str = str(offset).strip() if offset is not None else ""
            reg_name_str = str(reg_name).strip() if reg_name is not None else ""
            
            print(f"  -> offset_str: '{offset_str}' (是否为空: {not offset_str})")
            print(f"  -> reg_name_str: '{reg_name_str}' (是否为空: {not reg_name_str})")
            print(f"  -> 是否为寄存器行: {bool(offset_str and reg_name_str)}")
            
            if offset_str and reg_name_str:
                current_register = reg_name_str
                print(f"  🆕 创建新寄存器: {current_register}")
            
            # 检查字段信息
            field_name_raw = row_data.get('field_name')
            field_name = str(field_name_raw).strip() if field_name_raw is not None else ""
            
            print(f"  -> field_name: '{field_name}' (是否为空: {not field_name})")
            
            if current_register and field_name:
                # 检查是否为保留字段
                if field_name.lower() in ['reserved', 'rsvd', '保留']:
                    print(f"  ⚪ 保留字段，跳过: {field_name}")
                else:
                    # 检查位范围
                    bit_range_raw = row_data.get('bit_range')
                    bit_range_str = str(bit_range_raw).strip() if bit_range_raw is not None else ""
                    
                    print(f"  -> bit_range: '{bit_range_str}' (是否为空: {not bit_range_str})")
                    
                    if bit_range_str:
                        field_count += 1
                        print(f"  ✅ 有效字段: {field_name} [{bit_range_str}] (总计: {field_count})")
                    else:
                        print(f"  ❌ 位范围为空，字段无效")
            elif field_name:
                print(f"  ⚠️  有字段名但无当前寄存器: {field_name}")
        
        print(f"\n📊 解析结果:")
        print(f"  有效字段总数: {field_count}")
        
        if hasattr(workbook, 'close'):
            workbook.close()
        
        return field_count
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return 0


def main():
    """主函数"""
    print("🔍 字段解析问题调试工具")
    print("=" * 50)
    
    if not (OPENPYXL_AVAILABLE and XLRD_AVAILABLE):
        print("❌ 缺少必要的依赖库")
        return
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 测试文件
    test_files = [
        'template_register_file.xlsx',
        'template_register_file.xls'
    ]
    
    results = {}
    
    for filename in test_files:
        file_path = os.path.join(current_dir, filename)
        if os.path.exists(file_path):
            field_count = debug_field_parsing(file_path)
            results[filename] = field_count
        else:
            print(f"❌ 文件不存在: {filename}")
    
    # 比较结果
    if len(results) == 2:
        xlsx_count = results.get('template_register_file.xlsx', 0)
        xls_count = results.get('template_register_file.xls', 0)
        
        print(f"\n{'='*60}")
        print("📊 字段解析结果比较:")
        print(f"{'='*60}")
        print(f"XLSX文件: {xlsx_count} 个字段")
        print(f"XLS文件:  {xls_count} 个字段")
        
        if xlsx_count == xls_count and xls_count > 0:
            print("✅ 字段解析结果一致且正常")
        elif xls_count == 0:
            print("❌ XLS文件字段解析失败")
        else:
            print("⚠️  字段解析结果不一致")


if __name__ == "__main__":
    main()
