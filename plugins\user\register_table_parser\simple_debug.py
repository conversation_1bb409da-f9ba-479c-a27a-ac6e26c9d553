#!/usr/bin/env python3
"""
简单的调试脚本，直接比较.xls和.xlsx文件的单元格值
"""

import os

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

try:
    import xlrd
    XLRD_AVAILABLE = True
except ImportError:
    XLRD_AVAILABLE = False


def compare_files():
    """比较.xls和.xlsx文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    xlsx_file = os.path.join(current_dir, 'template_register_file.xlsx')
    xls_file = os.path.join(current_dir, 'template_register_file.xls')
    
    if not os.path.exists(xlsx_file) or not os.path.exists(xls_file):
        print("❌ 测试文件不存在，请先运行 create_test_files.py")
        return
    
    print("🔍 比较.xlsx和.xls文件的关键单元格")
    print("=" * 50)
    
    # 加载.xlsx文件
    xlsx_data = {}
    if OPENPYXL_AVAILABLE:
        workbook = openpyxl.load_workbook(xlsx_file, data_only=True)
        worksheet = workbook.active
        
        # 提取关键单元格
        for row in range(10, 20):  # 第10-19行
            for col in [1, 2, 5, 8, 9, 10, 11, 12]:  # 关键列
                cell_value = worksheet.cell(row=row, column=col).value
                xlsx_data[(row, col)] = cell_value
        
        workbook.close()
        print("✅ 成功加载.xlsx文件")
    
    # 加载.xls文件
    xls_data = {}
    if XLRD_AVAILABLE:
        workbook = xlrd.open_workbook(xls_file)
        worksheet = workbook.sheet_by_index(0)
        
        # 提取关键单元格
        for row in range(10, 20):  # 第10-19行
            for col in [1, 2, 5, 8, 9, 10, 11, 12]:  # 关键列
                # xlrd使用0基索引
                xlrd_row = row - 1
                xlrd_col = col - 1
                
                if xlrd_row < worksheet.nrows and xlrd_col < worksheet.ncols:
                    cell_value = worksheet.cell_value(xlrd_row, xlrd_col)
                    xls_data[(row, col)] = cell_value
                else:
                    xls_data[(row, col)] = None
        
        print("✅ 成功加载.xls文件")
    
    # 比较数据
    print("\n📊 关键单元格比较:")
    print(f"{'位置':<8} {'列名':<12} {'XLSX值':<15} {'XLS值':<15} {'一致':<6}")
    print("-" * 70)
    
    column_names = {
        1: 'Offset',
        2: 'RegName', 
        5: 'Width',
        8: 'Bit',
        9: 'FieldName',
        10: 'RW',
        11: 'ResetValue',
        12: 'Set/Clear'
    }
    
    differences = []
    
    for row in range(10, 20):
        for col in [1, 2, 5, 8, 9, 10, 11, 12]:
            xlsx_val = xlsx_data.get((row, col))
            xls_val = xls_data.get((row, col))
            
            # 标准化值
            xlsx_str = str(xlsx_val).strip() if xlsx_val is not None else ""
            xls_str = str(xls_val).strip() if xls_val is not None else ""
            
            is_same = xlsx_str == xls_str
            
            if xlsx_str or xls_str:  # 只显示非空单元格
                status = "✅" if is_same else "❌"
                col_name = column_names.get(col, f"Col{col}")
                
                print(f"({row},{col})".ljust(8) + f" {col_name:<12} {xlsx_str[:13]:<15} {xls_str[:13]:<15} {status}")
                
                if not is_same:
                    differences.append((row, col, col_name, xlsx_val, xls_val))
    
    if differences:
        print(f"\n⚠️  发现 {len(differences)} 个差异:")
        for row, col, col_name, xlsx_val, xls_val in differences:
            print(f"  行{row} {col_name}: XLSX='{xlsx_val}' vs XLS='{xls_val}'")
    else:
        print("\n✅ 所有关键单元格都一致!")
    
    # 特别检查字段相关的数据
    print("\n🔍 字段相关数据检查:")
    field_rows = [12, 13, 14, 15, 16, 17, 18, 19]  # 可能包含字段的行
    
    for row in field_rows:
        field_name_xlsx = str(xlsx_data.get((row, 9), '')).strip()  # I列：FieldName
        field_name_xls = str(xls_data.get((row, 9), '')).strip()
        
        bit_range_xlsx = str(xlsx_data.get((row, 8), '')).strip()   # H列：Bit
        bit_range_xls = str(xls_data.get((row, 8), '')).strip()
        
        if field_name_xlsx or field_name_xls or bit_range_xlsx or bit_range_xls:
            print(f"  行{row}:")
            print(f"    字段名 - XLSX: '{field_name_xlsx}', XLS: '{field_name_xls}'")
            print(f"    位范围 - XLSX: '{bit_range_xlsx}', XLS: '{bit_range_xls}'")
            
            if field_name_xlsx != field_name_xls:
                print(f"    ❌ 字段名不一致!")
            if bit_range_xlsx != bit_range_xls:
                print(f"    ❌ 位范围不一致!")


def main():
    """主函数"""
    print("🔧 简单调试工具 - 比较.xls和.xlsx文件")
    print("=" * 50)
    
    # 检查依赖库
    if not OPENPYXL_AVAILABLE:
        print("❌ openpyxl不可用")
        return
    
    if not XLRD_AVAILABLE:
        print("❌ xlrd不可用")
        return
    
    compare_files()


if __name__ == "__main__":
    main()
